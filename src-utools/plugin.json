{"version": "1.0.0", "author": "<PERSON><PERSON><PERSON>", "homepage": "111", "main": "dist/index.html", "logo": "dist/logo.png", "preload": "src/preload.js", "features": [{"code": "ui.router?router=uploadFileHome", "explain": "图床", "cmds": ["图床"]}, {"code": "ui.router?router=fileBox", "explain": "图床文件盒子", "cmds": ["box", "图床文件盒子"]}, {"code": "upload.base64", "cmds": [{"type": "img", "label": "图片上传"}]}, {"code": "upload.clipboard", "cmds": ["从剪贴板上传图片"]}, {"code": "upload.file", "cmds": [{"type": "files", "label": "图片上传", "match": "/.*(jpg|png|jpeg|gif|webp)$/i", "fileType": "file", "minLength": 1}, {"type": "files", "label": "文件上传", "match": "/^(?!.*\\.(jpg|png|jpeg|gif|webp)$).*$/i", "fileType": "file", "minLength": 1}]}], "development": {"main": "http://localhost:6001/index.html"}}