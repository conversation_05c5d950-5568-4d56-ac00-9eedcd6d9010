const {  clipboard, nativeImage } = require('electron');
window.clipboard = clipboard;
window.nativeImage = nativeImage;
window.path = require('path');
window.nodeBuffer = require('node:buffer');
// 请求库
window.https = require('https');
window.http = require('http');
window.net = require('net');
window.nodeProcess = process;
window.nodeRequire = require;
// 这里使用申请权限方式, 不将所有 fs 方法都挂载到 window 上
window.fs = require('./lib/fs')(['statSync', 'readFileSync', 'writeFileSync',
  'existsSync', 'mkdirSync', 'rmSync', 'rename',
  'createWriteStream', 'createReadStream', 'readdirSync', 'copyFile']);
window.zip = require('cross-zip');
