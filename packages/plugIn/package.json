{"name": "@xiaou66/picture-plugin", "version": "0.0.40", "description": "Picture bed plugin system", "main": "./dist/index.cjs.js", "module": "./dist/index.es.js", "types": "./dist/index.d.ts", "publishConfig": {"access": "public"}, "files": ["dist"], "scripts": {"build": "vite build", "dev": "vite", "prepare": "npm run build", "publish": "npm publish"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "devDependencies": {"typescript": "^5.0.0", "vite": "^6.3.4", "vite-plugin-dts": "^4.5.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.es.js", "require": "./dist/index.cjs.js"}}}