{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "lib": ["ESNext", "DOM"], "moduleResolution": "bundler", "strict": true, "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "noEmit": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "types": ["utools-api-types"]}, "include": ["src"], "exclude": ["node_modules", "dist"]}