import { IUploadFileParams } from '../storage'


export const stringVariable = [
  {
    label: '年',
    value: '{YY}',
    demo: () => {
      return new Date().getFullYear().toString();
    },
    format: (obj: IUploadFileParams) => {
      return new Date().getFullYear().toString();
    }
  },
  {
    label: '年',
    value: '{Y}',
    demo: () => {
      return new Date().getFullYear().toString().substring(2);
    },
    format: (obj: IUploadFileParams) => {
      return new Date().getFullYear().toString().substring(2);
    }
  },
  {
    label: '月',
    value: '{M}',
    demo: () => {
      return (new Date().getMonth() + 1).toString().padStart(2, '0');
    },
    format: (obj: IUploadFileParams) => {
      return (new Date().getMonth() + 1).toString().padStart(2, '0');
    }
  },
  {
    label: '日',
    value: '{D}',
    demo: () => {
      return (new Date().getDate()).toString().padStart(2, '0');
    },
    format: (obj: IUploadFileParams) => {
      return (new Date().getDate()).toString().padStart(2, '0');
    }
  },
  {
    label: '时',
    value: '{h}',
    demo: () => {
      return new Date().getHours().toString().padStart(2, '0');
    },
    format: (obj: IUploadFileParams) => {
      return new Date().getHours().toString().padStart(2, '0');
    }
  },
  {
    label: '分',
    value: '{m}',
    demo: () => {
      return new Date().getMinutes().toString().padStart(2, '0');
    },
    format: (obj: IUploadFileParams) => {
      return new Date().getMinutes().toString().padStart(2, '0');
    }
  },
  {
    label: '秒',
    value: '{s}',
    demo: () => {
      return new Date().getSeconds().toString().padStart(2, '0');
    },
    format: () => {
      return new Date().getSeconds().toString().padStart(2, '0');
    }
  },
  {
    label: '毫秒',
    value: '{ss}',
    demo: () => {
      return new Date().getMilliseconds().toString().padStart(3, '0');
    },
    format: (obj: IUploadFileParams) => {
      return new Date().getMilliseconds().toString().padStart(3, '0');
    }
  },
  {
    label: '时间戳',
    value: '{timestamp}',
    width: 300,
    demo: () => {
      return Date.now();
    },
    format: (obj: IUploadFileParams) => {
      return Date.now().toString();
    }
  },
  {
    label: '文件名称',
    value: '{filename}',
    width: 200,
    demo: () => {
      return '文本';
    },
    format: (obj: IUploadFileParams) => {
      return obj.fileName;
    }
  },
  {
    label: '后缀',
    value: '{suffix}',
    width: 200,
    demo: () => {
      return 'txt';
    },
    format: (obj: IUploadFileParams) => {
      return obj.suffix;
    }
  },
  {
    label: '全文件名',
    value: '{allFilename}',
    width: 300,
    demo: () => {
      return '文本.txt';
    },
    format: (obj: IUploadFileParams) => {
      return obj.allFileName;
    }
  },
  {
    label: '场景名称',
    value: '{sceneName}',
    width: 300,
    demo: () => {
      return '测试场景';
    },
    format: (obj: IUploadFileParams) => {
      return obj.sceneName || '';
    }
  },
  {
    label: '随机字符串',
    value: '{rand(5)}',
    width: 300,
    demo: () => {
      return generateRandomString(5);
    },
    format: (obj: IUploadFileParams) => {
      return generateRandomString(5);
    }
  },
];

// 生成指定长度的随机字符串
function generateRandomString(length: number): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
export function filePathFormat(str: string, obj: IUploadFileParams): string {
  console.log('filePathFormat', str, obj)
  stringVariable.forEach(variable => {
    // 获取变量的值
    const value = variable.format(obj) || '';
    // 使用正则表达式替换所有匹配的变量
    const regex = new RegExp(variable.value, 'g'); // 'g' 标志表示全局替换
    str = str.replace(regex, value);
  });

  // 处理动态变量 {rand(n)}
  const randRegex = /\{rand\((\d+)\)\}/g;
  str = str.replace(randRegex, (match, p1) => {
    const length = parseInt(p1, 10);
    return generateRandomString(length);
  });

  return str;
}
