//region 上传文件
export interface IUploadFileResult<E = any> {
  /**
   * 上传后文件访问地址
   */
  url: string;

  /**
   * 缩略图地址
   */
  thumbnailUrl?: string;

  /**
   * 额外数据
   */
  extra?: E;
}
export interface IUploadFileParams {
  /**
   * 上传 id
   */
  readonly id: string;

  /**
   * 配置 id
   */
  readonly storageId: string;

  /**
   * 配置名称
   */
  readonly sceneName: string;

  /**
   * 文件路径
   */
  readonly filePath: string;

  /**
   * 全文件名称
   */
  readonly allFileName: string;

  /**
   * 文件名称
   */
  readonly fileName: string;

  /**
   * 文件后缀
   */
  readonly suffix: string;

  /**
   * 文件大小
   */
  readonly fileSize: number;

  /**
   * 文件
   */
  readonly file?: File ;
}
//endregion


/**
 * 删除参数
 */
export interface IDeleteFileParams<E = any> {
  storageId: string;

  /**
   * 额外数据
   */
  extra?: E;
}
