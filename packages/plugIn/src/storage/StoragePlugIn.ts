import { IDeleteFileParams, IUploadFileParams, IUploadFileResult } from './StoragePlugInType'
import { filePathFormat } from '../utils'


/**
 * 插件接口
 */
interface IStoragePlugIn {

  /**
   * 插件初始化调用
   */
  init(errorCallback: ErrorCallbackType): void;

  /**
   * 插件销毁调用
   */
  destroy(): void;
}


type ErrorCallbackType = (name: string, action: string, error: Error) => void;

export interface UploadFileOptions {
   onProgress?: (progress: number) => void;
}

/**
 * 存储插件
 */
export abstract class StoragePlugIn<C = any, E = any> implements IStoragePlugIn {

  init(): void {
    this.doInit()
  }

  /**
   * 初始化使用 <br/>
   * constructor 方法之后调用, 在第一次使用前调用
   * @protected
   */
  protected abstract doInit(): void

  destroy() {
    this.doDestroy()
  }

  /**
   * 插件销毁调用, 用于销毁资源
   * @protected
   */
  protected abstract doDestroy(): void


  /**
   * 上传文件
   * @param params 参数
   * @param options 上传可选参数
   */
  abstract uploadFile(params: IUploadFileParams, options?: UploadFileOptions): Promise<IUploadFileResult<E>>


  /**
   * 删除文件 <br/>
   * 如果要实现删除文件请实现这个接口
   * @param params 参数
   */
  public deleteFile(params: IDeleteFileParams<E>): Promise<boolean> {
    return Promise.resolve(false);
  }

  /**
   * 格式化上传路径
   * @param templateStr 模版路径
   * @param params 上传参数
   * @protected
   */
  protected formatUploadPath(templateStr: string, params: IUploadFileParams) {
    return filePathFormat(templateStr, params);
  }

  /**
   * 从 URL 中提取路径
   * @param url 完整的图片 URL
   * @returns 图片路径
   */
  protected extractImagePath(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.pathname;
    } catch (error) {
      // 如果 URL 解析失败，尝试使用正则表达式
      const matches = url.match(/https?:\/\/[^\/]+(\/.*)/);
      return matches ? matches[1] : '';
    }
  }


  /**
   * 校验配置 <br/>
   * 如果不通过抛出异常
   * @param config
   * @protected
   */
  public verifyConfig(config: C): Promise<void> {
    return Promise.resolve(undefined);
  }

  protected readConfig(storageId: string): C {
    const key = `storageSource/${storageId}`
    return utools.dbCryptoStorage.getItem(key)
  }

  protected saveConfig(storageId: string, config: C): void {
    const key = `storageSource/${storageId}`;
    utools.dbCryptoStorage.setItem(key, config);
  }

  /**
   * 插件被卸载
   */
  uninstall() {

  }
}
