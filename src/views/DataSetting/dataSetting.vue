<script lang="ts" setup>
import {ref, toRefs, h} from "vue";
import {Message, Modal, Notification} from "@arco-design/web-vue";
import {useDataSettingStore} from "@/stores";

const { localAppDataPath } = toRefs(useDataSettingStore());
const model = ref({});

function handlerOpenLocalAppDataPath() {
  utools.shellShowItemInFolder(localAppDataPath.value);
}


// 删除插件所有数据
function handleDeleteAllPluginData() {
  Modal.warning({
    title: '二次确认',
    content: '确认要删除插件内所有的数据?',
    okText: '删除',
    cancelText: '取消',
    hideCancel: false,
    okButtonProps: {
      status: 'danger'
    },
    onOk(e) {
      utools.db.allDocs()
          .map(({_id}) => utools.dbStorage.removeItem(_id))
      window.fs.rmSync(localAppDataPath.value, {
        recursive: true,
        force: true
      });
      utools.getFeatures()
          .map(({ code }) => utools.removeFeature(code));
      Notification.success({
        title: '提示',
        content: '清理成功, 需要完全退出插件后再进入即可'
      });
      // @ts-ignore
      utools.outPlugin(true);
    },
  });
}

function handleTransferLocalAppDataPath() {
  Modal.info({
    title: '迁移数据目录提示',
    content: '选择目前不能是当前目录子目录, 选择的目录必须是空目录',
    okText: '我知道了',
    onOk: handleTransferLocalAppDataPathInner,
  });
}
function handleTransferLocalAppDataPathInner() {
  const newPaths = utools.showOpenDialog({
    title: '请选择迁移到目录',
    properties: ['createDirectory', 'openDirectory']
  });
  if (!newPaths || !newPaths.length) {
    return;
  }

  const newPath = newPaths[0];
  // if (newPath === localAppDataPath.value) {
  //   return;
  // }
  //
  // if (newPath.startsWith(localAppDataPath.value)) {
  //   Message.warning("新目录不能是之前目录的子目录");
  //   return;
  // }

  if (window.fs.readdirSync(newPath).length > 0) {
    Message.warning("选择的目录必须是空目录");
    return;
  }

  const updateDataModal = Modal.info({
    title: '迁移确认',
    content: () => {
      return h('div', {},
          [
            h('div', {}, '迁移数据请不要关闭插件, 避免造成无法预料的后果'),
            h('div', {}, [
              h('span', {}, '目标路径: '),
              h('span', {}, newPath)
            ])
          ])
    },
    okText: '开始迁移(请勿中途退出)',
    onBeforeOk() {
      this.okText = '迁移中...开始压缩原文件';
      this.okLoading = true;
      const zipFilePath = window.path.join(window.path.dirname(localAppDataPath.value), "picture-bed-plus.zip");
      window.zip.zip(localAppDataPath.value, zipFilePath, () => {
        this.okText = '迁移中...开始移动文件';
        console.log('zipFilePath', zipFilePath, newPath);
        const newPathZip =  window.path.join(window.path.dirname(newPath), "picture-bed-plus.zip");
        window.zip.zip(localAppDataPath.value, zipFilePath, () => {
          console.log('zipFilePath', zipFilePath)
          this.okText = '迁移中...开始移动文件';
          console.log('zipFilePath', zipFilePath, newPath);
          const newPathZip =  window.path.join(window.path.dirname(newPath), "screenshot-plus.zip");
          window.fs.copyFile(zipFilePath,  newPathZip, (err: any) => {
            if (err) {
              Message.error("迁移文件出现问题" + err.message);
              return;
            }
            this.okText = '迁移中...开始解压文件';
            window.zip.unzip(newPathZip, newPath, (error: any) => {
              if (error) {
                Message.error("解压文件文件出现问题" + error.message);
                return;
              }
              window.fs.rmSync(localAppDataPath.value, {
                recursive: true,
                force: true
              });
              window.fs.rmSync(newPathZip);
              window.fs.rmSync(zipFilePath);
              localAppDataPath.value = newPath;
              Message.success("迁移完成");
              updateDataModal.close();
            });
          });
        });
      });
    },
  });
}
</script>
<template>
  <div class="u-main-content">
    <a-page-header :show-back="false">
      <template #title>数据管理</template>
      <template #subtitle>
        这里是可以管理插件数据, 如果需要管理插件数据均在这里配置
      </template>
    </a-page-header>
    <div class="u-main-content-inner">
      <div class="u-gap10 u-radius10"
           style="padding: 14px; flex-direction: column; background: var(--color-bg-3);">
        <div class="u-fx u-fac u-f-between u-mb12">
          <div class="u-bold">插件数据目录</div>
          <div class="u-fx u-fac u-gap10">
            <a-link size="small"
                    @click="handleTransferLocalAppDataPath">
              <iconpark-icon name="switch" style="padding-right: 4px;"></iconpark-icon>
              迁移位置
            </a-link>
            <a-link @click="handlerOpenLocalAppDataPath">
              <iconpark-icon name="folder-open" style="padding-right: 4px;"></iconpark-icon>
              打开
            </a-link>
          </div>
        </div>
        <div>
          <a-input :model-value="localAppDataPath" style="width: 100%" size="small"  readonly/>
        </div>
        <div class="u-tips u-mt10">存储插件额外需要支持库及运行时部分缓存, 无需清理插件会定期清理</div>
      </div>
      <div class="u-gap10 u-radius10 u-mt10"
           style="padding: 14px; flex-direction: column; background: var(--color-bg-3);">
        <div class="u-fx u-fac u-f-between u-mb12">
          <div class="u-bold">数据清理</div>
          <div>
            <a-button size="small"
                      status="danger"
                      @click="handleDeleteAllPluginData">
              <template #icon>
                <iconpark-icon name="delete" style="padding-top: 6px" />
              </template>
              清理插件内数据
            </a-button>
          </div>
        </div>
        <div class="u-tips u-mt10">数据清理操作不可恢复，请谨慎操作</div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.tips {
  margin-bottom: 16px;
}
</style>
