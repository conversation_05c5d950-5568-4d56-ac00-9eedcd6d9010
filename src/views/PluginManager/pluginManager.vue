<script lang="ts" setup>
import { onMounted, ref, toRefs } from 'vue'
import type { StoragePlugInConfig } from '@xiaou66/picture-plugin'
import { usePluginStore } from '@/stores/PluginStore/PluginStore.ts'
import { Message } from '@arco-design/web-vue'
import type { PlugInfo } from '@xiaou66/picture-plugin'
import { useUserSettingStore } from '@/stores'

const pluginStore = usePluginStore()
const { pluginAutoUpdate } = toRefs(useUserSettingStore())

function loadLocalPlugIn() {
  const dirPathList = utools.showOpenDialog({
    title: '打开文件',
    properties: ['openDirectory'],
  })
  if (!dirPathList || dirPathList.length == 0) {
    return
  }
  const dirPath = dirPathList[0]
  window.storagePlugInManager.loadPlugin(dirPath)
  pluginStore.loadLocalManualPlugin(dirPath)
  loadAllPluginConfig()
}

const allPluginConfig = ref<StoragePlugInConfig[]>([])

function loadAllPluginConfig() {
 window.storagePlugInManager.getAllPluginConfig()
    .then(res => {
      allPluginConfig.value = res;
    });
}

const updatePluginCodeList = ref<string[]>([])

async function refreshUpdatePluginList() {
  updatePluginCodeList.value = await window.storagePlugInManager.getAllUpdatePluginList()
}

function handleUpdatePlugin(pluginInfo: PlugInfo) {
  window.storagePlugInManager.install(pluginInfo.pluginCode).then(() => {
    Message.success(`「${pluginInfo.pluginName}」更新完成`)
    refreshUpdatePluginList()
    loadAllPluginConfig()
  })
}

onMounted(() => {
  loadAllPluginConfig()
  refreshUpdatePluginList()
})

// 卸载插件物理
function handleUninstallPlugin(plugInConfig: StoragePlugInConfig) {
  window.storagePlugInManager.uninstallPlugin(plugInConfig.pluginInfo.pluginCode);
  loadAllPluginConfig();
  Message.success('卸载成功');
}

const dev = utools.isDev()
</script>
<template>
  <div class="u-main-content">
    <div class="u-fx u-fac u-f-between u-mb12">
      <div class="u-bold">插件列表</div>
      <div class="u-fx u-gap10">
        <a-tooltip content="启动插件自动检查更新" position="top">
          <a-checkbox v-model:model-value="pluginAutoUpdate"> 自动更新 </a-checkbox>
        </a-tooltip>
        <a-button v-if="dev" shape="round" @click="() => loadLocalPlugIn()">
          加载本地插件
        </a-button>
      </div>
    </div>
    <div v-if="allPluginConfig.length">
      <div v-for="item in allPluginConfig"
           :key="item.pluginInfo.pluginCode"
           class="plugin-item">
        <div class="plugin-container">
          <div class="plugin-info">
            <div class="plugin-logo">
              <img :src="item.pluginInfo.pluginLogo" alt="" />
            </div>
            <div class="plugin-details">
              <div class="plugin-name">{{ item.pluginInfo.pluginName }}</div>
              <div class="plugin-desc">{{ item.pluginInfo.pluginDesc }}</div>
            </div>
          </div>
          <div class="plugin-version">
            <a-badge color="green" :text="item.pluginInfo.pluginVersion" />
          </div>
          <div class="u-fx u-gap10">
            <a-popconfirm
              position="left"
              type="info"
              content="确认要卸载吗?"
              @ok="() => handleUninstallPlugin(item)"
            >
              <a-button size="mini"
                        shape="round"
                        status="danger"
                        class="uninstall-btn">
                卸载
              </a-button>
            </a-popconfirm>
            <a-button
              v-if="updatePluginCodeList.includes(item.pluginInfo.pluginCode)"
              size="mini"
              shape="round"
              @click="() => handleUpdatePlugin(item.pluginInfo)"
            >
              更新
            </a-button>
          </div>
        </div>
      </div>
    </div>
    <div v-else>
      <a-empty style="padding-top: 100px;">
        <div class="u-mb10">无安装的插件</div>
      </a-empty>
    </div>
  </div>
</template>
<style lang="less" scoped>
.plugin-item {
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.plugin-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: var(--main-background);
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.plugin-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.plugin-logo {
  img {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    object-fit: cover;
  }
}

.plugin-details {
  width: 360px;
  .plugin-name {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
  }

  .plugin-desc {
    font-size: 12px;
    color: var(--text-tips-color);
  }
}

.plugin-version {
  margin: 0 24px;
}

.uninstall-btn {
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
}
</style>
