<script setup lang="ts">

import { LeftMenuSplitPanel } from '@/components/common'
import UploadSourceSaveDrawer from './templates/StorageSourceSaveDrawer.vue';
import { onMounted, ref, useTemplateRef, watch } from 'vue'
import { type StorageSourceItem, useStorageSourceStore } from '@/stores'
import StorageSourceDetail from './templates/StorageSourceDetail.vue'
import type {
  StorageSourceSaveDrawerInstance,
} from './templates/StorageSourceSaveDrawer.ts'
import { Modal } from '@arco-design/web-vue'


const props = defineProps<{
  mode?: 'add'
}>();

const storageSourceRef = useTemplateRef<StorageSourceSaveDrawerInstance>('storageSourceRef');

const storageSourceStore = useStorageSourceStore();

const activeId = ref();
function setActiveId(id: string) {
  activeId.value = id;
}

function handleDeleteStorageSource(storageSource: StorageSourceItem) {
  Modal.confirm({
    escToClose: false,
    closable: false,
    maskClosable: false,
    title: '二次确认',
    content: `是否删除「${storageSource.storageName}」存储源, 删除后无法还原`,
    onBeforeOk: async () => {
      storageSourceStore.deleteStorageSource(storageSource.id);
      return true;
    }
  });
}
onMounted(() => {
  if (storageSourceStore.storageSourceList.length > 0) {
    activeId.value = storageSourceStore.storageSourceList[0].id
  }
});

watch(() => props.mode, () => {
  if (props.mode === 'add') {
    storageSourceRef.value?.show();
  }
});
onMounted(() => {
  if (props.mode === 'add') {
    storageSourceRef.value?.show();
  }
});
</script>

<template>
  <div class="u-main-content-no-padding">
    <UploadSourceSaveDrawer ref="storageSourceRef"
                            @saveOk="(id) => setActiveId(id)">
    </UploadSourceSaveDrawer>
    <LeftMenuSplitPanel>
      <template #left>
        <div class="container">
          <div class="u-fx u-fac u-f-between u-mb10">
            <div class="u-font-size-smail"
                 style="padding-left: 8px;">上传源</div>
            <div>
              <a-button style="width: 100%;"
                        size="mini"
                        shape="round"
                        @click="() => storageSourceRef!.show()">
                <template #icon>
                  <iconpark-icon style="padding-top: 5px;" :size="14" name="plus" />
                </template>
                添加
              </a-button>
            </div>
          </div>
          <div class="menu-sub">
            <div
                v-for="item in storageSourceStore.storageSourceList"
                :key="item.id"
                 class="u-fx u-fac u-gap10 menu-item"
                 style="justify-content: space-between"
                 :class="{ active: activeId === item.id}"
                 @click="() => setActiveId(item.id)">
              <div class="u-fx u-fac u-gap10" style="font-size: 13px">
                <div>{{ item.storageName }}</div>
              </div>
              <div>
                <a-dropdown :button-props="{ type: 'text', size: 'mini' }">
                  <template #content>
                    <a-doption @click="() => storageSourceRef?.show(item)">
                      <template #icon>
                        <iconpark-icon name="write" />
                      </template>
                      <template #default>
                        <span>编辑</span>
                      </template>
                    </a-doption>
                    <a-doption style="color: #f15;"  @click="() => handleDeleteStorageSource(item)">
                      <template #icon>
                        <iconpark-icon name="delete" />
                      </template>
                      <template #default>
                        <span>删除</span>
                      </template>
                    </a-doption>
                  </template>
                  <iconpark-icon style="padding-top: 4px" name="more-one" />
                </a-dropdown>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #content>
        <StorageSourceDetail v-if="activeId"
                             :storage-id="activeId"
                             :storageSourceRef="storageSourceRef!">
        </StorageSourceDetail>
      </template>
    </LeftMenuSplitPanel>
  </div>
</template>

<style scoped lang="less">
.container {
  box-sizing: border-box;
  padding: 4px;
  height: 100%;
  background: var(--left-menu-background-color);
}
// 手势菜单
.menu-sub {
  display: flex;
  flex-direction: column;
  gap: 4px;
  .menu-item {
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    iconpark-icon {
      font-size: 16px;
    }
    &:hover {
      background-color: var(--select-hover);
    }

    // 选中状态
    &.active {
      background-color: var(--select-hover);
    }
  }
}
</style>
