<script setup lang="ts">

import type { StorageSourceSaveDrawerInstance } from './StorageSourceSaveDrawer.ts'
import { type StorageSourceItem, useStorageSourceStore } from '@/stores'
import { onMounted, ref, toRaw, useTemplateRef, watch, defineAsyncComponent } from 'vue'
import type { StoragePlugInConfig } from '@xiaou66/picture-plugin';
import { type FormInstance, Message } from '@arco-design/web-vue'
import { InputFileFormat } from '../../../components/plugin/InputFormat'
import { useMultiLoading, useSingleLoading } from '@/hooks/useLoading.ts'
import { RadioGroup } from '../../../components/plugin/RadioGroup'
import { InputButton } from '../../../components/plugin/Button'

const props = defineProps<{
  storageId: string;
  storageSourceRef: StorageSourceSaveDrawerInstance,
}>();

const storageSourceStore = useStorageSourceStore();

const storageSource = ref<StorageSourceItem>();
const pluginConfig = ref<StoragePlugInConfig>();
async function loadStorageSource() {
  if (props.storageId) {
    storageSource.value = storageSourceStore.getStorageSourceById(props.storageId);
    pluginConfig.value = await window.storagePlugInManager
      .getPluginConfig(storageSource.value.storagePluginCode);
    const key = `storageSource/${props.storageId}`;
    console.log(' pluginConfig.value',  pluginConfig.value);
    form.value = utools.dbCryptoStorage.getItem(key) || {};
  }
}

watch(() => props.storageId, () => {
  loadStorageSource().then(() => {});
});

onMounted(() => {
  loadStorageSource().then(() => {});
});

function reloadStorageConfig() {
  loadStorageSource();
}
const form = ref<Record<string, any>>({});
const formRef = useTemplateRef<FormInstance>('formRef');

const [handleSaveConfig, saveLoading] = useSingleLoading(async () => {
  const error = await formRef.value!.validate();
  const key = `storageSource/${props.storageId}`;
  if (error) {
    return;
  }
  const storageSource = storageSourceStore.getStorageSourceById(props.storageId);
  const pluginInstance = window.storagePlugInManager.getPluginInstance(storageSource.storagePluginCode);
  debugger;
  try {
    await pluginInstance.verifyConfig(form.value)
  }catch (error: any) {
    return;
  }
  utools.dbCryptoStorage.setItem(key, toRaw(form.value));
  Message.success('保存成功');
});

function handleCopyText() {
  utools.copyText(props.storageId);
  Message.success("复制成功");
}
const isDev = utools.isDev();

const [handleInstallPlugin, installStatus] = useMultiLoading(async (pluginCode: string) => {
  await window.storagePlugInManager.install(pluginCode);
  await loadStorageSource();
}, (pluginCode) => pluginCode);
</script>

<template>
  <div class="u-pos-rel config-container-wrapper">
    <div v-if="storageId" class="config-container">
      <div class="u-fx u-f-between u-mb10">
        <div></div>
        <div class="u-fx u-gap10">
          <a-tooltip content="本地服务使用">
            <a-button size="small" shape="round" @click="handleCopyText()">
              复制 ID
            </a-button>
          </a-tooltip>
          <a-button v-if="isDev"
                    size="small"
                    shape="round"
                    @click="reloadStorageConfig">
            重新加载配置
          </a-button>
          <a-button shape="round"
                    size="small"
                    :loading="saveLoading"
                    @click="handleSaveConfig">
            <template #icon>
              <iconpark-icon style="padding-top: 6px;"
                             name="save-one" />
            </template>
            保存
          </a-button>
        </div>
      </div>
      <div>
        <div v-if="pluginConfig && pluginConfig.uiConfig">
          <div v-if="pluginConfig.uiConfig.tips" class="u-mb10">
            <a-alert title="提示">
              <div v-html="pluginConfig.uiConfig.tips"></div>
            </a-alert>
          </div>
          <a-form ref="formRef"
                  v-model:model="form"
                  auto-label-width>
            <a-form-item v-for="(formItem, index) in pluginConfig.uiConfig.forms"
                         :key="index" v-bind="formItem.formItem">
              <a-input v-if="formItem.type === 'input'"
                       v-bind="formItem.elementProperty"
                       v-model:model-value="form[formItem.formItem.field]" />
              <InputFileFormat v-else-if="formItem.type === 'input-format'"
                               :form-item="formItem"
                               v-model:model-value="form[formItem.formItem.field]" />
              <RadioGroup v-else-if="formItem.type === 'radio-group'"
                          :form-item="formItem"
                          v-model:model-value="form[formItem.formItem.field]" />
              <InputButton v-else-if="formItem.type === 'input-button'"
                           :form-item="formItem"
                           v-model:model-value="form" />
              <component v-else-if="formItem.type === 'custom' && formItem.customComponent"
                         :is="defineAsyncComponent(formItem.customComponent)"
                         :form-item="formItem"
                         v-model:model-value="form[formItem.formItem.field]" />
            </a-form-item>
          </a-form>
        </div>
        <div v-else-if="storageSource">
          <a-empty>
            <div class="u-mb10">
              缺少 {{storageSource.storagePluginCode}} 插件无法使用
            </div>
            <div>
              <a-button
                size="small"
                shape="round"
                :loading="installStatus(storageSource!.storagePluginCode)"
                @click="() => handleInstallPlugin(storageSource!.storagePluginCode)">
                点击下载
              </a-button>
            </div>
          </a-empty>
        </div>
      </div>
    </div>
    <div v-else>
      <a-empty style="padding-top: 100px;">
        <div class="u-mb10">暂时无上传源</div>
        <div>
          <a-button @click="() => storageSourceRef!.show()"
                    shape="round" size="small">
            添加存储源
          </a-button>
        </div>
      </a-empty>
    </div>
  </div>
</template>

<style scoped lang="less">
.config-container-wrapper {
  width: 100%;
  height: 100%;
  .config-container {
    position: absolute;
    width: 88%;
    left: 50%;
    padding: 10px;
    border-radius: 10px;
    transform: translateX(-50%);
  }
}
.empty-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
:deep(.arco-alert-title) {
  font-size: 14px;
}
</style>
