<script setup lang="ts">
import { LeftMenuSplitPanel } from '@/components/common'
import UploadSceneSaveModal from './templates/UploadSceneSaveModal.vue'
import { onMounted, ref, useTemplateRef } from 'vue'
import type { UploadSceneSaveModalInstance } from './templates/UploadSceneSaveModal.ts';
import { type SceneInfoItem, useSceneStore } from '@/stores'
import UploadSceneDetail from './templates/UploadSceneDetail.vue'
import { Message, Modal } from '@arco-design/web-vue'
import UploadSceneFlowDetail from '@/views/UploadScene/templates/UploadSceneFlowDetail.vue'


const uploadSceneSaveModalRef = useTemplateRef<UploadSceneSaveModalInstance>('uploadSceneSaveModalRef');

const sceneStore = useSceneStore();
const activeId = ref();
function setActiveId(id: string) {
  activeId.value = id;
}
onMounted(() => {
  if (sceneStore.sceneInfoList.length) {
    activeId.value = sceneStore.sceneInfoList[0].id;
  }
});

function handleDeleteScene(sceneInfo: SceneInfoItem) {
  const sceneStore = useSceneStore();
  Modal.confirm({
    escToClose: false,
    closable: false,
    maskClosable: false,
    title: '二次确认',
    content: `是否删除「${sceneInfo.sceneName}」场景`,
    onBeforeOk: async () => {
      sceneStore.removeScene(sceneInfo.id);
      if (sceneStore.sceneInfoList.length) {
        activeId.value = sceneStore.sceneInfoList[0].id;
      }
      return true;
    }
  });
}
function handleAddScene() {
  if (sceneStore.sceneInfoList.length >= 6) {
    Message.info("你已经创建了 6 个场景了. 目前仅支持 6 个场景");
    return;
  }
  uploadSceneSaveModalRef.value?.show()
}
</script>

<template>
  <UploadSceneSaveModal ref="uploadSceneSaveModalRef"
                        @saveOk="(id) => setActiveId(id)" />
  <div class="u-main-content-no-padding">
    <LeftMenuSplitPanel>
      <template #left>
        <div class="container">
          <div class="u-fx u-fac u-f-between u-mb10">
            <div class="u-font-size-smail"
                 style="padding-left: 8px;">上传场景</div>
            <div>
              <a-button style="width: 100%;"
                        size="mini"
                        shape="round"
                        @click="handleAddScene">
                <template #icon>
                  <iconpark-icon style="padding-top: 5px;" :size="14" name="plus" />
                </template>
                添加
              </a-button>
            </div>
          </div>
          <div class="menu-sub">
            <div v-for="scene in sceneStore.sceneInfoList"
                 :key="scene.id"
                 class="u-fx u-fac u-gap10 menu-item"
                 style="justify-content: space-between"
                 :class="{ active: activeId === scene.id}"
                 @click="() => setActiveId(scene.id)">
              <div class="u-fx u-fac u-gap10" style="font-size: 13px">
                <div>{{ scene.sceneName }}</div>
              </div>
              <div>
                <a-dropdown :button-props="{ type: 'text', size: 'mini' }">
                  <template #content>
                    <a-doption style="color: #f15;"  @click="() => handleDeleteScene(scene)">
                      <template #icon>
                        <iconpark-icon name="delete" />
                      </template>
                      <template #default>
                        <span>删除</span>
                      </template>
                    </a-doption>
                  </template>
                  <iconpark-icon style="padding-top: 4px" name="more-one" />
                </a-dropdown>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #content>
        <UploadSceneFlowDetail
          v-if="activeId"
          :scene-id="activeId" />
<!--        <UploadSceneDetail v-if="activeId"
                           :sceneId="activeId" />-->
        <div v-else>
          <a-empty style="padding-top: 100px;">
            <div class="u-mb10">无场景</div>
            <div>
              <a-button @click="() => uploadSceneSaveModalRef!.show()" shape="round" size="small">
                添加场景
              </a-button>
            </div>
          </a-empty>
        </div>
      </template>
    </LeftMenuSplitPanel>
  </div>
</template>

<style scoped lang="less">
.container {
  box-sizing: border-box;
  padding: 4px;
  height: 100%;
  background: var(--left-menu-background-color);
}
// 手势菜单
.menu-sub {
  display: flex;
  flex-direction: column;
  gap: 4px;
  .menu-item {
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    iconpark-icon {
      font-size: 16px;
    }
    &:hover {
      background-color: var(--select-hover);
    }

    // 选中状态
    &.active {
      background-color: var(--select-hover);
    }
  }
}
</style>
