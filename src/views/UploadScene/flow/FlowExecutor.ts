import type { IFlowContext } from './node/BaseFlowNode.ts';
import {useSceneStore} from "@/stores";
import { FLOW_NODE_ACTIONS_MAP } from "@/views/UploadScene/flow/node";

export class FlowExecutor {

  public static async executor(flowId: string, initContext: IFlowContext) {
    const sceneStore = useSceneStore();
    const flow = sceneStore.getSceneFlowDetail(flowId);
    if (!flow) {
      throw new Error('场景不存在')
    }
    for (const node of flow.nodeList) {
      if (!FLOW_NODE_ACTIONS_MAP[node.nodeCode]) {
        // 节点不支持/下线了
        continue;
      }

      const execute = FLOW_NODE_ACTIONS_MAP[node.nodeCode].execute;

      if (execute) {
        await execute(flow, node, initContext);
      }
    }
  }
  public static async adjustFlowNodeData() {
    /*debugger;
    const flowItems = await ExtensionManager.getFlowInstance().getFlowList();
    for (const flowItem of flowItems) {

      const all = flowItem.nodeList.map(node => {
        const adjustData = FLOW_NODE_ACTIONS_MAP[node.nodeCode].adjustData;
        if (adjustData) {
          return adjustData(flowItem, node);
        }
        return null;
      }).filter(item => item);
      const res = await Promise.all(all);
      if (res.filter(item => item).length > 0) {
        await ExtensionManager.getFlowInstance().saveFlow(flowItem);
      }
    }*/
  }
}
