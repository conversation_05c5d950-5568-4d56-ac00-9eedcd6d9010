import { type AsyncComponentLoader, inject } from 'vue';
import  { provide } from 'vue';
import type { IFlowItem, IFlowNode } from '@/views/UploadScene/flow'
import type { FileUpdateInfoItem } from "@/@types";
import type {FileLibraryItem, SceneInfoItem} from "@/stores";

export interface IFlowNodeInfo {
  code: string;
  title: string;
  group: '触发' | '通用' | '完成';
  nodeType: 'trigger' | 'common';
  end?: boolean;
  desc?: string;
}

export interface IFlowNodeConfig{
  info: IFlowNodeInfo;
  nodeComponent: AsyncComponentLoader;
}

export interface IFlowContext {
  uploadFile: FileUpdateInfoItem,
  fileLibrary: FileLibraryItem,
  sceneInfo: SceneInfoItem,
  context?: Record<string, any>,
}

export interface IFlowNodeActions<T> {
  code: string;
  execute?: (flow: IFlowItem, flowData: IFlowNode<T>, data: IFlowContext) => Promise<void>;
  onSaveData?: (flow: IFlowItem, flowData: IFlowNode<T>) => void;
  onDestroy?: (flow: IFlowItem, flowData: IFlowNode<T>) => void;
  adjustData?: (flow: IFlowItem, flowData: IFlowNode<T>) => Promise<boolean>;
}

export interface FlowProvide {
  removeNode: (id: string) => void;
  updateNodeField: (id: string, field: string, value: any) => void;
  updateNode: (id: string, nodeData: Record<string, any>) => void;
  createNode: (node: IFlowNodeConfig) => void;
}


const FLOW_PROVIDE_CODE = Symbol('flowProvide');


export function flowProvide(provideContent: FlowProvide) {
  provide<FlowProvide>(FLOW_PROVIDE_CODE, provideContent);
}

export function useFlowInject(): FlowProvide {
  return inject<FlowProvide>(FLOW_PROVIDE_CODE)!;
}
