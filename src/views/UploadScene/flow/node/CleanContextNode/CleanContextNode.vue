<script setup lang="ts">
import { CONFIG, type ICleanContextData } from './CleanContextNode.ts'
import BaseFlowNode from '@/views/UploadScene/flow/node/BaseFlowNode.vue'
import type { IFlowNode } from '@/views/UploadScene/flow'
import { SettingItem } from '@/components/common'

const props = defineProps<{
  node: IFlowNode<ICleanContextData>;
}>();
</script>

<template>
  <BaseFlowNode v-bind="CONFIG.info"
                :id="node.id">
    <SettingItem name="清空之前上传存储源内容"
                 min-name
                 background="transparent"
                 :click="true"
                 @click="node!.nodeData.clearFileUploadInfo = !node.nodeData.clearFileUploadInfo">
      <a-switch v-model:model-value="node!.nodeData.clearFileUploadInfo"
                checked-color="rgb(var(--green-6))"
                style="width: 48px;"
                @click.stop />
    </SettingItem>
  </BaseFlowNode>
</template>

<style scoped lang="less">

</style>
