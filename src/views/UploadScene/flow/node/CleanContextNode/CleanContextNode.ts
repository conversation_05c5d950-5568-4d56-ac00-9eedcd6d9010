import type { IFlowNodeActions, IFlowNodeConfig } from '../BaseFlowNode.ts';
import FileUploadUtils from "@/utils/FileUploadUtils.ts";
import type { FileUploadInfo } from "@/stores";
import type {FileUpdateInfoItem} from "@/@types";


const CODE: string = "CleanContext";

export interface ICleanContextData {
  /**
   * 存储源 ids
   */
  clearFileUploadInfo?: string[];
}
export const CONFIG: IFlowNodeConfig = {
  info: {
    code: CODE,
    title: '清理数据',
    desc: '将清理流程中产生数据, 解决数据的干扰',
    group: '通用',
    nodeType: 'common',
  },
  nodeComponent: async () => import('./CleanContextNode.vue'),
}


export const ACTIONS: IFlowNodeActions<ICleanContextData> = {
  code: CODE,
  async execute(flow, flowData, data) {
    if (!flowData.nodeData) {
      console.log('StorageSourceSelect 缺少配置')
      return;
    }
    if (flowData.nodeData.clearFileUploadInfo) {
      data.fileLibrary.fileUploadInfoList.length = 0;
    }
  }
}
