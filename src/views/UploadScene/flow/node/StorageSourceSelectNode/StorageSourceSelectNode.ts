import type { IFlowNodeActions, IFlowNodeConfig } from '../BaseFlowNode.ts';
import FileUploadUtils from "@/utils/FileUploadUtils.ts";
import type { FileUploadInfo } from "@/stores";
import type {FileUpdateInfoItem} from "@/@types";


const CODE: string = "storageSource";

export interface IStorageSourceSelectData {
  /**
   * 存储源 ids
   */
  storageSourceIds?: string[];
}
export const CONFIG: IFlowNodeConfig = {
  info: {
    code: CODE,
    title: '存储源',
    desc: '选择将文件上传到的存储源',
    group: '通用',
    nodeType: 'common',
  },
  nodeComponent: async () => import('./StorageSourceSelectNode.vue'),
}


/**
 * 上传场景文件
 * @param uploadFile 上传文件信息
 * @param data 配置信息
 * @param sceneInfo
 * @constructor
 */
async function uploadSceneFile(
  uploadFile: FileUpdateInfoItem,
  data: IStorageSourceSelectData,
) {
  const storageSourceIds = data.storageSourceIds!;
  return (
    await Promise.all(
      storageSourceIds.map((storageSourceId) => {
        return FileUploadUtils.uploadStorageSourceFile(uploadFile, storageSourceId)
      }),
    )
  ).filter((item) => item) as FileUploadInfo[]
}

export const ACTIONS: IFlowNodeActions<IStorageSourceSelectData> = {
  code: CODE,
  async execute(flow, flowData, data) {
    if (!flowData.nodeData
      || !flowData.nodeData.storageSourceIds
      || !flowData.nodeData.storageSourceIds.length) {
      console.log('StorageSourceSelect 缺少配置')
      return;
    }

    const fileUploadInfos = await uploadSceneFile(data.uploadFile, flowData.nodeData);
    // 将上传后文件列表加入到 fileLibrary 中
    data.fileLibrary.fileUploadInfoList.push(...fileUploadInfos);
  }
}
