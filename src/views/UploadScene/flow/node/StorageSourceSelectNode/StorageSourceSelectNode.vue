<script setup lang="ts">
import BaseFlowNode from '../BaseFlowNode.vue'
import { CONFIG, type IStorageSourceSelectData } from './StorageSourceSelectNode.ts'
import type { IFlowNode } from '@/views/UploadScene/flow'
import { useStorageSourceStore } from '@/stores'

const props = defineProps<{
  node: IFlowNode<IStorageSourceSelectData>;
}>();

const storageSourceStore = useStorageSourceStore();
</script>

<template>
  <BaseFlowNode v-bind="CONFIG.info"
                :id="node.id">
    <div class="u-fx u-fac u-gap5">
      <a-select v-model:model-value="node.nodeData!.storageSourceIds"
                size="small"
                multiple>
        <template #prefix>存储源</template>
        <a-option
          v-for="item in storageSourceStore.storageSourceList"
          :key="item.id"
          :value="item.id"
          :label="item.storageName"
        />
      </a-select>
    </div>
  </BaseFlowNode>
</template>

<style scoped lang="less">

</style>
