<script setup lang="ts">
import BaseFlowNode from '../BaseFlowNode.vue'
import { CONFIG, type IFileBoxSelectData } from './FileBoxSelectNode.ts'
import type { IFlowNode } from '@/views/UploadScene/flow'
import { useFileBoxStore } from '@/stores'
const props = defineProps<{
  node: IFlowNode<IFileBoxSelectData>;
}>();

const fileBoxStore = useFileBoxStore()
</script>

<template>
  <BaseFlowNode v-bind="CONFIG.info"
                :id="node.id">
    <div class="u-fx u-fac u-gap5 u-mb10">
      <a-select  v-model:model-value="node!.nodeData.fileBoxId"
                size="small"
                allow-clear>
        <template #prefix>盒子</template>
        <a-option
          v-for="item in fileBoxStore.fileBoxInfoList"
          :key="item.id"
          :value="item.id"
          :label="item.fileBoxName"
        />
      </a-select>
    </div>
  </BaseFlowNode>
</template>

<style scoped lang="less">
</style>
