import type { IFlowNodeActions, IFlowNodeConfig } from '../BaseFlowNode.ts';
import {useFileBoxStore} from "@/stores";
const CODE: string = "fileBoxSelect";

export interface IFileBoxSelectData {
  fileBoxId?: string;
}
export const CONFIG: IFlowNodeConfig = {
  info: {
    code: CODE,
    title: '文件盒子',
    desc: '将文件存储到指定文件盒子',
    group: '通用',
    nodeType: 'common',
  },
  nodeComponent: async () => import('./FileBoxSelectNode.vue'),
}

export const ACTIONS: IFlowNodeActions<IFileBoxSelectData> = {
  code: CODE,
  async execute(flow, flowData, data) {
    if (!flowData.nodeData || !flowData.nodeData.fileBoxId) {
      console.log('fileBoxSelect 缺少配置');
      return;
    }
    const nodeData = flowData.nodeData;
    const fileBoxStore = useFileBoxStore();
    data.fileLibrary.fileBoxId = nodeData.fileBoxId;
    fileBoxStore.addFileLibraryItem(data.fileLibrary);
  }
}
