import type { IFlowNodeActions, IFlowNodeConfig, IFlowNodeInfo } from './BaseFlowNode.ts';
import { groupBy, keyBy } from 'es-toolkit';
import { shallowRef, defineAsyncComponent } from 'vue';
import type { Component } from 'vue';

function importToList(obj: Record<string, any>) {
  return Object.values(obj).map(item => item);
}

export const FLOW_NODE_LIST: Readonly<IFlowNodeConfig[]> = importToList(import.meta.glob('./**/index.ts', {
  eager: true,
  import: 'CONFIG'
}));

export const SELECT_GROUP_SORT_LIST: string[] = ['通用'];
export const FLOW_NODE_MAP: Readonly<Record<string, IFlowNodeConfig>> = keyBy(FLOW_NODE_LIST, (item) => item.info.code);



export const FLOW_NODE_GROUP: Readonly<Record<string, IFlowNodeConfig[]>> = groupBy(FLOW_NODE_LIST, (item) => item.info.group);



export const FLOW_NODE_ACTIONS_LIST: Readonly<IFlowNodeActions<any>[]> = importToList(import.meta.glob('./**/index.ts', {
  eager: true,
  import: 'ACTIONS'
}));


export const FLOW_NODE_ACTIONS_MAP: Readonly<Record<string, IFlowNodeActions<any>>> = keyBy(FLOW_NODE_ACTIONS_LIST, (item) => item.code);


// 预加载并缓存所有组件
export const FLOW_COMPONENT_CACHE = shallowRef<Record<string, Component>>(FLOW_NODE_LIST.reduce((obj, flowNodeConfig) => {
  const {info: {code}, nodeComponent} = flowNodeConfig;
  if (nodeComponent) {
    obj[code] = defineAsyncComponent({
      loader: nodeComponent,
      // 添加错误处理
      onError(error, retry, fail, attempts) {
        console.error(`Failed to load component ${code}:`, error);
        if (attempts <= 3) {
          retry();
        } else {
          fail();
        }
      },
    });
  }
  return obj;
}, {} as Record<string, any>));
