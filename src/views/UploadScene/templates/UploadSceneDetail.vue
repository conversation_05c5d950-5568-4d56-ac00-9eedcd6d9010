<script setup lang="ts">
import { onMounted, ref, useTemplateRef, watch } from 'vue'
import { useFileBoxStore, useSceneStore, useStorageSourceStore } from '@/stores'
import { type FormInstance, Message } from '@arco-design/web-vue'
import { cloneDeep } from 'es-toolkit'

const props = defineProps<{
  sceneId: string;
}>();


const defaultFormValue = {
  storageSourceIds: [],
  fileBoxId: '',
};
const form = ref<{
  storageSourceIds: string[];
  fileBoxId: string;
}>(defaultFormValue);

function loadSceneDetailItem() {
  const res = sceneStore.getSceneDetailItem(props.sceneId);
  console.log(res)
  form.value = cloneDeep(defaultFormValue);
  if (res) {
    form.value = {
      ...form.value,
      ...res,
    }
  }
}
watch(() => props.sceneId, () => {
  loadSceneDetailItem();
});
onMounted(() => {
  loadSceneDetailItem();
});

const storageSourceStore = useStorageSourceStore();

const sceneStore = useSceneStore();
const fileBoxStore = useFileBoxStore();
const formRef = useTemplateRef<FormInstance>('formRef');
async function handleSaveConfig() {
  const error = await formRef.value?.validate();
  if (error) {
    return;
  }

  sceneStore.saveSceneDetail({
    id: props.sceneId,
    ...form.value,
  });

  Message.success('保存成功');
}

function handleCopyText() {
  utools.copyText(props.sceneId);
  Message.success("复制成功");
}
</script>

<template>
  <div v-if="sceneId" class="scene-detail">
    <div class="u-fx u-f-between u-mb12">
      <div></div>
      <div class="u-fx u-gap10">
        <a-tooltip content="本地服务使用">
          <a-button size="small"
                    shape="round"
                    @click="handleCopyText()">
            复制 ID
          </a-button>
        </a-tooltip>
        <a-button size="small"
                  shape="round"
                  @click="handleSaveConfig">
          <template #icon>
            <iconpark-icon style="padding-top: 6px" name="save-one" />
          </template>
          保存
        </a-button>
      </div>
    </div>
    <a-form ref="formRef"
            v-model:model="form"
            auto-label-width>
      <a-form-item
        label="存储源列表"
        field="storageSourceIds"
        :rules="[{ required: true, message: '存储源列表是必选的' }]"
      >
        <a-select v-model:model-value="form.storageSourceIds"
                  size="small"
                  multiple>
          <a-option
            v-for="item in storageSourceStore.storageSourceList"
            :key="item.id"
            :value="item.id"
            :label="item.storageName"
          />
        </a-select>
      </a-form-item>
      <a-form-item label="文件盒子">
        <a-select v-model:model-value="form.fileBoxId"
                  size="small"
                  allow-clear>
          <a-option
            v-for="item in fileBoxStore.fileBoxInfoList"
            :key="item.id"
            :value="item.id"
            :label="item.fileBoxName"
          />
        </a-select>
      </a-form-item>
    </a-form>
  </div>
</template>

<style scoped lang="less">
.scene-detail {
  padding: 15px;
}
</style>
