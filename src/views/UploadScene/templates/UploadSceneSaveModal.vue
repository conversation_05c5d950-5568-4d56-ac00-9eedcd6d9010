<script setup lang="ts">
import {nextTick, ref, useTemplateRef} from 'vue'
import type { UploadSceneSaveModalInstance } from './UploadSceneSaveModal.ts'
import type { FormInstance } from '@arco-design/web-vue'
import { type SceneInfoItem, useSceneStore } from '@/stores'
import { cloneDeep } from 'es-toolkit'

const visible = ref(false);
const defaultFormValue: SceneInfoItem  = {
  id: '',
  sceneName: '',
  enable: false,
};
const form = ref(cloneDeep(defaultFormValue));
function show(sceneInfo?: SceneInfoItem) {
  form.value = cloneDeep(defaultFormValue);
  if (sceneInfo) {
    form.value = {
      ...form.value,
      ...sceneInfo,
    }
  }
  visible.value = true;
  nextTick(() => {
    sceneNameInputRef.value?.focus()
  })
}
defineExpose<UploadSceneSaveModalInstance>({
  show,
});
const formRef = useTemplateRef<FormInstance>('formRef')
async function handleVerifyForm() {
  const res = await formRef.value?.validate();
  if (res) {
    return false;
  }
  const id = sceneStore.saveSceneInfo(form.value)
  emits('saveOk', id);
  visible.value = false;
}
const sceneStore = useSceneStore();

const emits = defineEmits<{
  saveOk: [id: string];
}>()

const sceneNameInputRef = useTemplateRef<HTMLInputElement>('sceneNameInputRef');



</script>

<template>
  <a-modal v-model:visible="visible"
           ok-text="保存"
           @before-ok="handleVerifyForm"
           simple>
    <template #title>
      保存上传场景
    </template>
    <a-form v-model:model="form"
            ref="formRef"
            auto-label-width>
      <a-form-item
        label="场景名称"
        field="sceneName"
        :rules="[{ required: true, message: '请取一个好听场景名称' }]">
        <a-input ref="sceneNameInputRef"
                 v-model:model-value="form.sceneName"
                 @pressEnter="handleVerifyForm" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<style scoped lang="less">

</style>
