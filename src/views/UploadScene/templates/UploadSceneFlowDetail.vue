<script setup lang="ts">
import { cloneDeep } from 'es-toolkit'
import type { SaveFlowItem } from '@/views/UploadScene/flow'
import {
  FLOW_COMPONENT_CACHE,
  FLOW_NODE_ACTIONS_MAP,
  FLOW_NODE_MAP,
  type IFlowItem,
} from '@/views/UploadScene/flow'
import { nanoid } from 'nanoid'
import { nextTick, onMounted, ref, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import FlowSelectNode from './FlowSelectNode/FlowSelectNode.vue'
import { flowProvide, type IFlowNodeConfig } from '@/views/UploadScene/flow/node/BaseFlowNode.ts'
import type { SceneInfoItem } from '@/stores'
import { useSceneStore } from '@/stores'

const props = defineProps<{
  sceneId: string
}>()

const emits = defineEmits<{
  refresh: []
}>()

const sceneStore = useSceneStore()

const defaultValue: SaveFlowItem = {
  nodeList: [] as any,
}

const flowActiveIds = ref<string[]>([])
const flowData = ref<SaveFlowItem>(cloneDeep<SaveFlowItem>(defaultValue))
const sceneInfo = ref<SceneInfoItem>()
function loadData(sceneId: string) {
  sceneInfo.value = cloneDeep<SceneInfoItem>(sceneStore.getSceneInfoById(sceneId) || ({} as any))
  flowData.value = sceneStore.getSceneFlowDetail(sceneId) || cloneDeep<SaveFlowItem>(defaultValue)
}
watch(
  () => props.sceneId,
  async (sceneId: string) => {
    console.log(sceneId)
    loadData(sceneId)
  },
  { deep: true },
)
onMounted(() => {
  loadData(props.sceneId)
})

async function saveFlowItem() {
  if (!sceneInfo.value) {
    return
  }
  const { sceneName } = sceneInfo.value
  if (!sceneName) {
    Message.warning('请输入场景名称')
    return
  }

  const flowId = sceneStore.saveSceneInfo(sceneInfo.value)
  flowData.value.id = flowId;
  sceneStore.saveSceneFlowDetail(flowData.value);
  const flowItem = sceneStore.getSceneFlowDetail(flowId)!;
  if (flowItem.nodeList && flowItem.nodeList.length > 0) {
    for (const node of flowItem.nodeList) {
      const saveData =  FLOW_NODE_ACTIONS_MAP[node.nodeCode].onSaveData;
      if (saveData) {
        saveData(flowItem, node);
      }
    }
  }
  Message.success('保存成功')
  emits('refresh')
}

function updateNode(id: string, nodeData: Record<string, any>) {
  const index = flowData.value.nodeList.findIndex((item) => item.id === id)
  if (index !== -1) {
    const oldData = flowData.value.nodeList[index]
    flowData.value.nodeList.splice(index, 1, {
      ...oldData,
      nodeData: { ...oldData.nodeData, ...nodeData },
    })
  }
}
flowProvide({
  createNode(node: IFlowNodeConfig) {
    console.log('handleCreateFlowNode', node)
    const newNode = {
      id: nanoid(),
      nodeCode: node.info.code,
      nodeData: {},
    }
    const nodeList = flowData.value.nodeList
    const index = nodeList.findIndex((item) => FLOW_NODE_MAP[item.nodeCode].info.end)
    if (index === -1) {
      // 使用不可变更新方式，避免直接修改原数组
      flowData.value.nodeList = [...nodeList, newNode]
    } else {
      flowData.value.nodeList.splice(index, 0, newNode)
    }
    // 使用 nextTick 确保 DOM 更新完成后再进行其他操作
    nextTick(() => {
      // 可以在这里添加一些后续操作，比如滚动到新添加的节点
      flowActiveIds.value.push(newNode.id)
    })
  },
  updateNodeField(id, field: string, value: any) {
    updateNode(id, { [field]: value })
  },
  updateNode,
  removeNode(id) {
    const index = flowData.value.nodeList.findIndex((item) => item.id === id)
    if (index !== -1) {
      const node = flowData.value.nodeList[index]
      if (flowData.value.id) {
        // 保存过才出发销毁方法, 清理之前保存的数据及指令
        FLOW_NODE_ACTIONS_MAP[node.nodeCode]?.onDestroy?.(flowData.value as IFlowItem, node)
      }
      flowData.value.nodeList.splice(index, 1)
    }
  },
})
</script>

<template>
  <div class="collect-scene-detail">
    <div v-if="sceneInfo" class="u-fx u-fac u-f-between u-mb10">
      <div class="u-fx u-fac">
        <a-input
          v-model:model-value="sceneInfo.sceneName"
          class="u-transparent"
          size="small"
          placeholder="请输入场景名称"
        ></a-input>
      </div>
      <div class="u-fx u-fac u-gap10">
        <!--        <div class="u-fx u-fac u-gap5">
          <div style="font-size: 13px">启用</div>
          <a-switch v-model:model-value="sceneInfo.enable"
                    size="small"
                    checked-color="#52c41a" />
        </div>-->
        <a-button
          id="collect-scene-save"
          size="mini"
          type="outline"
          shape="round"
          @click="saveFlowItem"
        >
          <template #icon>
            <div class="u-fx u-fac">
              <iconpark-icon name="save-one"></iconpark-icon>
            </div>
          </template>
          保存
        </a-button>
      </div>
    </div>
    <a-collapse v-model:active-key="flowActiveIds">
      <a-scrollbar style="height: calc(100vh - 160px); overflow-y: auto">
        <div class="flow-node-wrapper">
          <div v-for="node in flowData.nodeList" :key="node.id">
            <Component :is="FLOW_COMPONENT_CACHE[node.nodeCode]" :node="node"> </Component>
          </div>
          <FlowSelectNode v-model:node-list="flowData.nodeList" />
          <!--          <Component-->
          <!--            v-if="endNode"-->
          <!--            :is="FLOW_COMPONENT_CACHE[endNode.nodeCode]"-->
          <!--            :node="endNode">-->
          <!--          </Component>-->
        </div>
      </a-scrollbar>
    </a-collapse>
  </div>
</template>

<style scoped lang="less">
.collect-scene-detail {
  padding: 10px;
}
.flow-node-wrapper {
  display: grid;
  gap: 10px;
}

.component-loading {
  padding: 20px;
  text-align: center;
  color: var(--color-text-3);
  background: var(--color-fill-2);
  border-radius: 6px;
  border: 1px dashed var(--color-border);
}
</style>
