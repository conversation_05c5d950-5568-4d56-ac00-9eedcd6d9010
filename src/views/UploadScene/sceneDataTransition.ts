import {useSceneStore} from "@/stores";
import type { SaveFlowItem } from "@/views/UploadScene/flow";
import {nanoid} from "nanoid";

export function sceneDataTransitionV2() {
  const sceneDataVersion = utools.dbStorage.getItem<number>('ops/sceneDataVersion') || 1;
  if (sceneDataVersion >= 2) {
    return;
  }
  const sceneStore = useSceneStore();
  // 清洗场景基本配置数据
  for (const sceneInfo of sceneStore.sceneInfoList) {
    sceneInfo.enable = true
  }

  // 清洗场景配置
  for (const sceneInfo of sceneStore.sceneInfoList) {
    const flowItem: SaveFlowItem = {
      id: sceneInfo.id,
      nodeList: [],
    }
    const sceneDetailItem = sceneStore.getSceneDetailItem(sceneInfo.id);
    if (sceneDetailItem) {
      if (sceneDetailItem.storageSourceIds && sceneDetailItem.storageSourceIds.length) {
        flowItem.nodeList.push({
          id: nanoid(),
          nodeCode: 'storageSource',
          nodeData: {
            storageSourceIds: sceneDetailItem.storageSourceIds
          }
        })
      }
      if (sceneDetailItem.fileBoxId) {
        flowItem.nodeList.push({
          id: nanoid(),
          nodeCode: 'fileBoxSelect',
          nodeData: {
            fileBoxId: sceneDetailItem.fileBoxId
          }
        })
      }
      sceneStore.saveSceneFlowDetail(flowItem);
      utools.dbStorage.removeItem(`sceneConfig/${sceneDetailItem.id}`);
    }
  }

  console.log('sceneDataTransitionV2')
  utools.dbStorage.setItem('ops/sceneDataVersion', 2);
}
