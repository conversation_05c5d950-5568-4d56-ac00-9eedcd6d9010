<script setup lang="ts">
import { ref, useTemplateRef } from 'vue'
import type { FileBoxSaveModalInstance } from './FileBoxSaveModal.ts'
import type { FormInstance } from '@arco-design/web-vue'
import { type FileBoxInfoItem, useFileBoxStore } from '@/stores'
import { cloneDeep } from 'es-toolkit'

const visible = ref(false);
const defaultFormValue = {
  id: '',
  fileBoxName: '',
}
const form = ref(cloneDeep(defaultFormValue));
function show(fileBoxInfo?: FileBoxInfoItem) {
  form.value = cloneDeep(defaultFormValue);
  if (fileBoxInfo) {
    form.value = {
      ...form.value,
      ...fileBoxInfo,
    }
  }
  console.log('form', form.value);
  visible.value = true;
}
defineExpose<FileBoxSaveModalInstance>({
  show,
});

const formRef = useTemplateRef<FormInstance>('formRef')
async function handleVerifyForm() {
  const res = await formRef.value?.validate();
  return res === undefined;
}

const fileBoxStore = useFileBoxStore();
const emits = defineEmits<{
  saveOk: [id: string];
}>()
function handleOk() {
  const id = fileBoxStore.saveFileBoxInfo(form.value);
  emits('saveOk', id);
}
</script>

<template>
  <a-modal v-model:visible="visible"
           ok-text="保存"
           @before-ok="handleVerifyForm"
           @ok="handleOk"
           simple>
    <template #title>
      保存文件盒子
    </template>
    <a-form v-model:model="form"
            ref="formRef"
            auto-label-width>
      <a-form-item label="盒子名称" :rules="[
        {required: true, message: '请取一个好听盒子名称'}
        ]">
        <a-input v-model:model-value="form.fileBoxName"></a-input>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<style scoped lang="less">

</style>
