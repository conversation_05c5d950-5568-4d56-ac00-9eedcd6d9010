<script setup lang="ts">
import { LeftMenuSplitPanel } from '@/components/common';
import { type FileBoxInfoItem, useFileBoxStore } from '@/stores'
import { onMounted, ref, useTemplateRef } from 'vue'
import FileBoxSaveModal from './templates/FileBoxSaveModal.vue'
import type { FileBoxSaveModalInstance } from './templates/FileBoxSaveModal.ts'
import FileBoxDetail from '@/views/FileBox/templates/FileBoxDetail.vue'
import { Modal } from '@arco-design/web-vue'

const fileBoxStore = useFileBoxStore();
const activeId = ref();
function setActiveId(id: string) {
  activeId.value = id;
}
onMounted(() => {
  if (fileBoxStore.fileBoxInfoList.length > 0) {
    activeId.value = fileBoxStore.fileBoxInfoList[0].id;
  }
})
const fileBoxSaveRef = useTemplateRef<FileBoxSaveModalInstance>('fileBoxSaveRef');
function handleDeleteFileBox(item: FileBoxInfoItem) {
  Modal.confirm({
    escToClose: false,
    closable: false,
    maskClosable: false,
    title: '二次确认',
    content: `是否删除「${item.fileBoxName}」盒子`,
    onBeforeOk: async () => {
      await fileBoxStore.removeFileBox(item.id);
      if (fileBoxStore.fileBoxInfoList.length > 0) {
        activeId.value = fileBoxStore.fileBoxInfoList[0].id;
      }
      return true;
    }
  });
}
</script>
<template>
  <FileBoxSaveModal ref="fileBoxSaveRef" @saveOk="(id) => setActiveId(id)" />
  <div class="u-main-content-no-padding">
    <LeftMenuSplitPanel>
      <template #left>
        <div class="container">
          <div class="u-fx u-fac u-f-between u-mb10">
            <div class="u-font-size-smail"
                 style="padding-left: 8px;">文件盒子</div>
            <div>
              <a-button style="width: 100%;"
                        size="mini"
                        shape="round"
                        @click="fileBoxSaveRef?.show()">
                <template #icon>
                  <iconpark-icon style="padding-top: 5px;" :size="14" name="plus" />
                </template>
                添加
              </a-button>
            </div>
          </div>
          <div class="menu-sub">
            <div v-for="item in fileBoxStore.fileBoxInfoList"
                 :key="item.id"
                 class="u-fx u-fac u-gap10 menu-item"
                 style="justify-content: space-between"
                 :class="{ active: activeId === item.id}"
                 @click="() => setActiveId(item.id)">
              <div class="u-fx u-fac u-gap10" style="font-size: 13px">
                <div>{{ item.fileBoxName }}</div>
              </div>
              <div>
                <a-dropdown :button-props="{ type: 'text', size: 'mini' }">
                  <template #content>
                    <a-doption @click="() => fileBoxSaveRef?.show(item)">
                      <template #icon>
                        <iconpark-icon name="write" />
                      </template>
                      <template #default>
                        <span>编辑</span>
                      </template>
                    </a-doption>
                    <a-doption style="color: #f15;"
                               @click="() => handleDeleteFileBox(item)">
                      <template #icon>
                        <iconpark-icon name="delete" />
                      </template>
                      <template #default>
                        <span>删除</span>
                      </template>
                    </a-doption>
                  </template>
                  <iconpark-icon style="padding-top: 4px" name="more-one" />
                </a-dropdown>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #content>
        <FileBoxDetail v-if="activeId"
                       :file-box-id="activeId" />
        <div v-else>
          <a-empty style="padding-top: 100px;">
            <div class="u-mb10">无场景</div>
            <div>
              <a-button @click="() => fileBoxSaveRef!.show()"
                        shape="round" size="small">
                添加文件盒子
              </a-button>
            </div>
          </a-empty>
        </div>
      </template>
    </LeftMenuSplitPanel>
  </div>
</template>
<style lang="less" scoped>
.container {
  box-sizing: border-box;
  padding: 4px;
  height: 100%;
  background: var(--left-menu-background-color);
}
// 手势菜单
.menu-sub {
  display: flex;
  flex-direction: column;
  gap: 4px;
  .menu-item {
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    iconpark-icon {
      font-size: 16px;
    }
    &:hover {
      background-color: var(--select-hover);
    }

    // 选中状态
    &.active {
      background-color: var(--select-hover);
    }
  }
}
</style>
