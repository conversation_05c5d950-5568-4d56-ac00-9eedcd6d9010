<script lang="ts" setup>
import { ref, toRefs } from 'vue'
import { useUserSettingStore } from '@/stores'
import { nanoid } from 'nanoid'
import { CopyUrlFormatSelect } from '@/components/business'
import { Message } from '@arco-design/web-vue'

const { formatUrlData, autoCopyUrlFormat } = toRefs(useUserSettingStore());

// 处理添加格式
function handleAddFormat() {
  formatUrlData.value.push({
    id: nanoid(),
    displayName: '',
    format: '',
    enable: false,
  })
}
// 处理删除格式
function handleDeleteFormat(id: string) {
  const index = formatUrlData.value.findIndex(item => item.id === id);
  if (index > -1) {
    formatUrlData.value.splice(index, 1)
  }
}

function handleFormatChange(value: boolean, index: number) {
  console.log(value, index)
  const { displayName, format } = formatUrlData.value[index]
  if (value && (!displayName || !format)) {
    Message.warning('显示名称和链接格式存在未填写无法开启')
    return;
  }
  if (!value &&  autoCopyUrlFormat.value ===  formatUrlData.value[index].id) {
    Message.warning('自动复制正在使用无法关闭')
    return;
  }
  formatUrlData.value[index].enable = value
}
</script>
<template>
 <div>
   <div class="u-fx u-mb6" style="justify-content: flex-end">
     <CopyUrlFormatSelect />
   </div>
   <a-table :data="formatUrlData"
            size="small"
            style="width: 100%"
            :pagination="false" :scroll="{ y: 280 }"
            :bordered="false">
     <template #columns>
       <a-table-column title="显示名称" :width="150">
         <template #cell="{record}">
           <a-input v-model="record.displayName"></a-input>
         </template>
       </a-table-column>
       <a-table-column title="链接格式">
         <template #cell="{record}">
           <a-input v-model="record.format"></a-input>
         </template>
       </a-table-column>
       <a-table-column title="启用" :width="66">
         <template #cell="{record, rowIndex}">
           <a-switch size="small"
                     :model-value="record.enable"
                     @change="(value) => handleFormatChange(value as any, rowIndex)"
                     checked-color="#69b076" />
         </template>
       </a-table-column>
       <a-table-column title="操作" :width="66">
         <template #title>
           <a-link status="normal"
                   style="font-size: 13px"
                   @click="handleAddFormat">新增</a-link>
         </template>
         <template #cell="{record}">
           <a-link v-if="!record.disableDelete && autoCopyUrlFormat !== record.id"
                   status="danger"
                   style="font-size: 13px"
                   @click="handleDeleteFormat(record.id)">删除</a-link>
           <a-tooltip v-else-if="!record.disableDelete"
                      content="自动复制正在使用无法删除"
                      position="left"
                      mini>
             <a-link status="danger"
                     style="font-size: 13px"
                     disabled
                     @click="handleDeleteFormat(record.id)">删除</a-link>
           </a-tooltip>
         </template>
       </a-table-column>
     </template>
   </a-table>
 </div>
</template>
<style lang="less" scoped>

</style>
