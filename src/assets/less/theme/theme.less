/*滚动条整体部分*/
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

/*滚动条的轨道*/
::-webkit-scrollbar-track {
  background-color: transparent;
}

::-webkit-scrollbar-track-piece {
  background-color: transparent;
}

/*滚动条里面的小方块，能向上向下移动*/
::-webkit-scrollbar-thumb {
  background-color: #9b97a2;
  border-radius: 1px;
  border: 1px solid #9b97a2;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
}

::-webkit-scrollbar-thumb:hover {
  background-color: #676e77;
  border: 1px solid #676e77;
}

::-webkit-scrollbar-thumb:active {
  background-color: #4E5969;
  border: 1px solid #4E5969;
}

/*边角，即两个滚动条的交汇处*/
::-webkit-scrollbar-corner {
  background-color: transparent;
}
@import "theme-dark";
@import "theme-light";
