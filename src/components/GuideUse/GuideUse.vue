<script setup lang="ts">
import { ref } from 'vue'

const visible = ref(true);
function handleOk() {

}
function handleClose() {
  utools.dbStorage.setItem('new', true);
}
</script>
<template>
  <a-modal v-model:visible="visible"
           :width="500"
           cancel-text="我要自己探索"
           ok-text="创建默认上传源"
           @ok="handleOk"
           @close="handleClose"
           :esc-to-close="false"
           :mask-closable="false"
           simple>
    <template #title>
      插件初始选择
    </template>
    <div>
      <div>「创建默认上传源」将会自动下载猫盒存储源插件并创建存储源</div>
      <div>「我要自己探索」 将不会做任何的初始化</div>
    </div>
  </a-modal>
</template>
<style lang="less" scoped>

</style>
