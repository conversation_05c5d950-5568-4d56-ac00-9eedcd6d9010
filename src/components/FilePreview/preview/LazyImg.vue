<template>
  <div class="lazy__box">
    <div class="lazy__resource">
      <img ref="lazyRef"
           class="lazy__img"
           :title="title"
           :alt="alt"
           @load="imageLoad"
           @error="imageError">
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, onMounted, onUnmounted, ref, watch } from 'vue'
import type { LazyType } from './types/lazy'
import type { Nullable } from './types/util'

const props = withDefaults(defineProps<{
  url: string;
  title: string;
  alt: string;
}>(), {
  url: '',
  title: '',
  alt: '',
});


const emit = defineEmits<{
  success: [url: string];
  error: [url: string];
  load: [url: string];
}>();

const imgLoaded = inject('imgLoaded') as () => void
const lazy = inject('lazy') as LazyType
const lazyRef = ref<Nullable<any>>(null)

onMounted(() => {
  render()
})

onUnmounted(() => {
  unRender()
})

// 使用 watch 监听 url的 变化
watch(() => props.url, () => {
  render()
})

function render() {
  if (!lazyRef.value)
    return

  lazy.mount(lazyRef.value, props.url, (status) => {
    imgLoaded()
    if (status)
      emit('success', props.url)
    else
      emit('error', props.url)
  })
}

function unRender() {
  if (!lazyRef.value)
    return

  lazy.unmount(lazyRef.value)
}

function imageLoad() {
  console.log('imageLoad')
  emit('load', props.url)
}

function imageError() {
  emit('error', props.url)
}
</script>

<style scoped>
.lazy__box {
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  overflow: hidden;
  position: relative;
}

.lazy__resource {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
}

.lazy__img {
  display: block;
}

.lazy__img[lazy="loading"] {
  padding: 5em 0;
  width: 48px;
}

.lazy__img[lazy="loaded"] {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.lazy__img[lazy="error"] {
  padding: 5em 0;
  width: 48px;
  height: auto;
}
img {
  object-fit: scale-down !important;
}
</style>
