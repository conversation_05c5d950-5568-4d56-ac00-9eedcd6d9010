<script setup lang="ts">
import { computed } from 'vue'
import FileConstants from '@/constant/FileConstants.ts'
import { LazyImg } from 'vue-waterfall-plugin-next'

const props = defineProps<{
  content: string;
  fileSuffix: string;
}>();

const isImage = computed<boolean>(() => {
  console.log('props.fileSuffix', props.fileSuffix, props.fileSuffix.toLowerCase())
  return FileConstants.IMAGE_TYPES.includes(props.fileSuffix.toLowerCase());
});

defineOptions({
  inheritAttrs: true
});

const emits = defineEmits<{
  mouseenter: [MouseEvent];
  mouseleave: [MouseEvent];
  click: [MouseEvent];
  focusin: [FocusEvent];
  focusout: [FocusEvent];
  contextmenu: [MouseEvent];
}>();
</script>

<template>
  <div @mouseenter="(e: MouseEvent) => emits('mouseenter', e)"
       @mouseleave="(e: MouseEvent) => emits('mouseleave', e)"
       @click="(e: MouseEvent) => emits('click', e)"
       @focusin="(e: FocusEvent) => emits('focusin', e)"
       @focusout="(e: FocusEvent) => emits('focusout', e)"
       @contextmenu="(e: MouseEvent) => emits('contextmenu', e)">
    <slot v-if="isImage" name="image" :src="content">
      <img alt="" :src="content" />
    </slot>
  </div>
</template>

<style scoped lang="less">
img {
  object-fit: contain;
}
</style>
