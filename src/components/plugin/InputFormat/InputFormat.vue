<script setup lang="ts">
import { nanoid } from 'nanoid'
import { filePathFormat } from '@xiaou66/picture-plugin'
import { ref, watch } from 'vue'
import type { IUploadFileParams } from '@xiaou66/picture-plugin'
import { Message } from '@arco-design/web-vue'

const modelValue = defineModel<string>('model-value');

const demoFormatResult = ref('');
watch(() => modelValue.value, (val) => {
  formatResult();
});
function formatResult() {
  demoFormatResult.value = filePathFormat(modelValue.value || '', {
    id: nanoid(),
    storageId: nanoid(),
    sceneName: '',
    filePath: '',
    allFileName: '',
    fileName: '',
    suffix: 'png',
    fileSize: 100,
    file: {} as File,
  });
  console.log('demoFormatResult', demoFormatResult.value)
}

const stringVariable = [
  {
    label: '年',
    value: '{YY}',
    demo: () => {
      return new Date().getFullYear().toString();
    },
    format: (obj: IUploadFileParams) => {
      return new Date().getFullYear().toString();
    }
  },
  {
    label: '年',
    value: '{Y}',
    demo: () => {
      return new Date().getFullYear().toString().substring(2);
    },
    format: (obj: IUploadFileParams) => {
      return new Date().getFullYear().toString().substring(2);
    }
  },
  {
    label: '月',
    value: '{M}',
    demo: () => {
      return (new Date().getMonth() + 1).toString().padStart(2, '0');
    },
    format: (obj: IUploadFileParams) => {
      return (new Date().getMonth() + 1).toString().padStart(2, '0');
    }
  },
  {
    label: '日',
    value: '{D}',
    demo: () => {
      return (new Date().getDate()).toString().padStart(2, '0');
    },
    format: (obj: IUploadFileParams) => {
      return (new Date().getDate()).toString().padStart(2, '0');
    }
  },
  {
    label: '时',
    value: '{h}',
    demo: () => {
      return new Date().getHours().toString().padStart(2, '0');
    },
    format: (obj: IUploadFileParams) => {
      return new Date().getHours().toString().padStart(2, '0');
    }
  },
  {
    label: '分',
    value: '{m}',
    demo: () => {
      console.log('new Date().getMinutes().toString().padStart(2, \'0\')', new Date().getMinutes().toString().padStart(2, '0'))
      return new Date().getMinutes().toString().padStart(2, '0');
    },
    format: (obj: IUploadFileParams) => {
      return new Date().getMinutes().toString().padStart(2, '0');
    }
  },
  {
    label: '秒',
    value: '{s}',
    demo: () => {
      return new Date().getSeconds().toString().padStart(2, '0');
    },
    format: () => {
      return new Date().getSeconds().toString().padStart(2, '0');
    }
  },
  {
    label: '毫秒',
    value: '{ss}',
    demo: () => {
      return new Date().getMilliseconds().toString().padStart(3, '0');
    },
    format: (obj: IUploadFileParams) => {
      return new Date().getMilliseconds().toString().padStart(3, '0');
    }
  },
  {
    label: '时间戳',
    value: '{timestamp}',
    width: 300,
    demo: () => {
      return Date.now();
    },
    format: (obj: IUploadFileParams) => {
      return Date.now().toString();
    }
  },
  {
    label: '后缀',
    value: '{suffix}',
    width: 200,
    demo: () => {
      return 'txt';
    },
    format: (obj: IUploadFileParams) => {
      return obj.suffix;
    }
  },
  {
    label: '场景名称',
    value: '{sceneName}',
    width: 300,
    demo: () => {
      return '测试场景';
    },
    format: (obj: IUploadFileParams) => {
      return obj.sceneName || '';
    }
  },
];

function copyText(text: string) {
  utools.copyText(text);
  Message.success("复制成功");
}
</script>

<template>
  <a-trigger trigger="hover"
             position="top"
             unmount-on-close>
    <template #content>
      <div class="input-format-trigger">
        <div class="u-font-size-smail">
          <div>文件路径</div>
          <div style="padding-top: 5px;">
            <div style="padding-top: 3px;">
              <span>结果: {{demoFormatResult}} </span>
            </div>
          </div>
          <div class="helper">
            <div style="padding: 5px 0;">常用格式</div>
            <div class="u-fx u-gap5" style="flex-direction: column">
              <div>
                <a-link  @click="() => modelValue = '{timestamp}.{suffix}'">{timestamp}.{suffix}</a-link>: 1738507241769.png
              </div>
              <div>
                <a-link  @click="() => modelValue = '{Y}{M}{D}{h}{m}{s}{ss}.{suffix}'">
                  {Y}{M}{D}{h}{m}{s}{ss}.{suffix}
                </a-link>: 250101120059.png
              </div>
            </div>
            <div style="padding: 5px 0;">变量</div>
            <div class="u-fx u-gap5 u-fac" style="flex-wrap: wrap">
              <div v-for="variable in stringVariable" :key="variable.value"
                   :style="{width: variable.width ? `${variable.width}px` : '100px'}">
                <a-link @click="() => copyText(variable.value)">{{variable.value}}</a-link> : {{variable.label}} : {{variable.demo()}}
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <a-input size="small" v-model:model-value="modelValue">
    </a-input>
  </a-trigger>
</template>

<style scoped lang="less">
.input-format-trigger {
  padding: 10px;
  width: 440px;
  background: var(--main-background);
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
  .helper {
    max-height: 190px;
    overflow-y: auto;
  }
}
</style>
