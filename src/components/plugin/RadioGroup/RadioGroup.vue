<script setup lang="ts">
import type { StorageUIFormElement } from '@xiaou66/picture-plugin'

const props = defineProps<{
  formItem: StorageUIFormElement
}>();

</script>
<template>
  <a-radio-group type="button"
                 :default-value="formItem.formItem.defaultValue">
    <template v-for="item in formItem.formItem.data"
              :key="item.value">
      <a-tooltip v-if="item.tooltip">
        <template #content>
          {{ item.tooltip }}
        </template>
        <a-radio :value="item.value">
          {{ item.label }}
        </a-radio>
      </a-tooltip>
      <a-radio v-else
               :value="item.value">
        {{ item.label }}
      </a-radio>
    </template>
  </a-radio-group>
</template>
<style lang="less" scoped>

</style>
