<script setup lang="ts">

</script>

<template>
  <div class="container-wrapper">
    <a-split :style="{
        height: '100%',
        width: '100%',
        minWidth: '500px',
      }"
             :default-size="150"
             :min="150"
    >
      <template #first>
        <a-typography-paragraph style="height: 100%;">
          <slot name="left"></slot>
        </a-typography-paragraph>
      </template>
      <template #second>
        <a-typography-paragraph style="height: 100%;">
          <slot name="content"></slot>
        </a-typography-paragraph>
      </template>
    </a-split>
  </div>
</template>

<style scoped lang="less">
.container-wrapper {
  width: 100%;
  height: 100%;
}
:deep(.arco-split-trigger-icon-wrapper) {
  background-color: var(--color-neutral-2);
}
:deep(div.arco-typography) {
  margin-bottom: 0;
}
</style>
