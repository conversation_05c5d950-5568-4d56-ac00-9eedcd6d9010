<script lang="ts" setup>
import { ref, useTemplateRef } from 'vue'
import { useIntersectionObserver } from '@vueuse/core'

const props = withDefaults(defineProps<{
  w?: string;
  h?: string;
  distance?: number;
}>(), {
  w: '100%',
  h: '100%',
  distance: 0,
});

const load = ref<boolean>(false);
const box = useTemplateRef<HTMLElement>('box');
const { stop } = useIntersectionObserver(box, ([{ isIntersecting }]) => {
  if (isIntersecting) { // 当内容可见
    load.value = true;
    stop();
  }
}, {
  rootMargin: `${props.distance}px`,
  threshold: 0,
});
</script>

<template>
  <slot v-if="load"></slot>
  <view v-else ref="box"
        :style="{ height: h, width: w }">
  </view>
</template>

