<script setup lang="ts">
defineProps<{
  title?: string;
}>()
</script>

<template>
  <div class="setting-item-group">
    <div class="header">
      <div class="title">
        {{ title }}
      </div>
      <div>
        <slot name="desc" />
      </div>
    </div>
    <div>
      <slot />
    </div>
  </div>
</template>

<style scoped lang="less">
.setting-item-group {
  > div:last-child {
    border-radius: 6px;
    overflow: hidden;
    transition: all 260ms linear;
    &:hover {
      box-shadow: rgba(0, 0, 0, 0.04) 0px 3px 5px;
    }
  }
  .header, .title {
    margin-bottom: 10px;
  }
  .title {
    font-size: 16px;
    font-weight: 600;
    border-left: 2px solid rgb(var(--arcoblue-5));
    padding-left: 8px;
  }
}
</style>
