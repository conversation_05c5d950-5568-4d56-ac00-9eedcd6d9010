<script setup lang="ts">
withDefaults(defineProps<{
  name?: string;
  desc?: string;
  click?: boolean;
  vip?: boolean;
  minName?: boolean;
  background?: string;
}>(), {
  background: 'var(--color-bg-3)'
});
const emits = defineEmits<{
  click: [];
}>()
function handleClick() {
  emits('click');
}
</script>
<template>
  <div class="item">
    <div class="u-fx u-f-between u-fac" :class="{ 'u-pointer': click }"
         @click.stop="handleClick">
      <div>
        <div class="name u-fx u-gap5 u-fac"  :class="{'u-font-size-smail': minName}">
          <slot name="name">
            {{ name }}
            <div v-if="vip">
              <img alt="" src="/vip.png" style="width: 14px; height: 14px;">
            </div>
          </slot>
        </div>
        <slot name="desc">
          <div class="tips">{{ desc }}</div>
        </slot>
      </div>
      <div>
        <slot name="default"></slot>
      </div>
    </div>
    <div>
      <slot name="extra"></slot>
    </div>
  </div>
</template>

<style scoped lang="less">
.item {
  background: v-bind(background);
  padding: 15px 10px;
  transition: all 300ms linear;
  user-select: none;
  &:hover {
    background: var(--color-neutral-1);
  }
  .name {
    font-weight: 600;
    margin-bottom: 4px;
  }
  .tips {
    font-size: 12px;
    color: var(--color-neutral-6);
  }
}
</style>
