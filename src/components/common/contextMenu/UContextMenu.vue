<script setup lang="ts">
import {ref, useTemplateRef, nextTick} from "vue";
import { useEventListener } from "@vueuse/core";
import type { Fn } from "@vueuse/core";
import type { UContextMenuInstance } from "./UContextMenu.ts";


const props = withDefaults(defineProps<{
  offset?: {left: number, top: number};
}>(), {
  offset: () => {
    return {
      left: 0,
      top: 0
    }
  }
});

let cleanEvent: null | Fn = null;

const emits = defineEmits<{
  hide: []
}>();
const position = ref<{left: number, top: number}>({
  left: 0,
  top: 0
});
const opacity = ref(0);
const contextRef = useTemplateRef<HTMLDivElement>('contextRef');

const visible = defineModel<boolean>('visible', {
  default: false,
});

function calcContextMenuPosition(pos: {left: number, top: number}) {
  position.value.left = pos.left - 10;
  const element = document.getElementById('contextRef');
  const menuHeight = element?.clientHeight || 0;
  const areaHeight = document.documentElement.clientHeight;
  const { top } = pos!;
  const { offset } = props;
  console.log('top + menuHeight > areaHeight', top + menuHeight > areaHeight);
  if (top + menuHeight + 30 > areaHeight) {
    // 当鼠标点击的y坐标加上菜单高度超出区域高度时
    position.value.top = top - menuHeight + 25 + offset.top;
  } else {
    position.value.top = top + offset.top;
  }

  const areaWidth = document.documentElement.clientWidth;
  const menuWidth = element?.clientWidth || 0;
  const { left } = pos!;
  if (left + menuWidth > areaWidth) {
    position.value.left = left - menuWidth + 10 + offset.left;
  } else {
    position.value.left = left - 10 + offset.left;
  }
  opacity.value = 1;
}
function show(e: MouseEvent) {
  visible.value = true;
  nextTick(() => {
    calcContextMenuPosition({ left: e.clientX, top: e.clientY });
  })
  cleanEvent = useEventListener(window, 'mousedown', (e) => {
    // .arco-dropdown
    if (!(e.target as HTMLElement)?.closest('.arco-dropdown')) {
      hide()
    }
  })
}

function showPosition(pos: { left: number, top: number }) {
  visible.value = true;
  setTimeout(() => {
    calcContextMenuPosition(pos);
  });
  cleanEvent = useEventListener(window, 'mousedown', (e) => {
    // .arco-dropdown
    if (!(e.target as HTMLElement)?.closest('.arco-dropdown')) {
      hide()
    }
  });
}

function hide() {
  cleanEvent && cleanEvent();
  cleanEvent = null;
  visible.value = false;
  opacity.value = 0;
  emits('hide');
}
function handleSelect() {
  hide();
}
defineExpose<UContextMenuInstance>({
  show,
  showPosition,
  hide,
});
</script>

<template>
  <a-dropdown trigger="contextMenu"
              alignPoint
              id="contextRef"
              class="u-transparent min-dropdown-select"
              :style="{display:'block', top: `${position.top}px`, left: `${position.left}px`, opacity}"
              :popup-visible="visible"
              @select="handleSelect">
    <slot></slot>
    <template #content>
      <div>
          <slot name="content"></slot>
      </div>
    </template>
  </a-dropdown>
</template>

<style scoped lang="less">

</style>
