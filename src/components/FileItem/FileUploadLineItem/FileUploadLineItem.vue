<script setup lang="ts">
import FileUtils from '@/utils/FileUtils.ts'
import {
  type FileLibraryItem,
  type FileUploadInfo,
  useFileBoxStore,
  useUploadFileListStore,
  useStorageSourceStore
} from '@/stores'
import { Message } from '@arco-design/web-vue'
import FileIconPreview from '@/components/FilePreview/FileIconPreview.vue'
import { fileLibraryConvertUrlItemList } from '@/utils/UrlFormatBuilder.ts'
defineProps<{
  fileLibrary: FileLibraryItem;
}>();

const storageSourceStore = useStorageSourceStore();
const uploadFileListStore = useUploadFileListStore();
const fileBoxStore = useFileBoxStore()
const emit = defineEmits<{
  afterDelete: [id: string];
}>();
async function handleFileDelete(fileLibrary: FileLibraryItem, remote: boolean) {

  if (remote) {
    // 远程删除
    for (const fileUploadInfo of fileLibrary.fileUploadInfoList) {
      const storageSource = storageSourceStore.getStorageSourceById(fileUploadInfo.storageId);
      if (storageSource) {
        try {
          await window.storagePlugInManager.getPluginInstance(storageSource.storagePluginCode)
            .deleteFile(fileUploadInfo);
        }catch (e) {
          debugger
          return;
        }
      } else {
        // 不存在
      }
    }

  }

  // 插件内文件数据移除
  uploadFileListStore.removeFileHistoryItemById(fileLibrary.id);
  fileBoxStore.removeFileLibrary(fileLibrary);
  emit('afterDelete', fileLibrary.id);
}

function formatUrl(uploadFile: FileLibraryItem, uploadInfo: FileUploadInfo) {
  return [
    { name: 'MD', url: `![${uploadFile.fileInfo.fileName}](${uploadInfo.url})` },
    { name: 'URL', url: uploadInfo.url }
  ]
}

function handleCopyText(url: string) {
  utools.copyText(url);
  Message.success('复制成功');
}
</script>

<template>
  <div class="upload-file-item" style="flex-direction: column">
    <div class="u-fx u-f-between u-mb12" style="width: 100%;">
      <div class="file-info">
        <div class="file-preview">
          <FileIconPreview :content="fileLibrary.fileUploadInfoList[0].url"
                           :file-suffix="fileLibrary.fileInfo.fileSuffix" />
        </div>
        <div class="file-details">
          <div class="file-name">{{ fileLibrary.fileInfo.fileName }}</div>
          <div class="file-size" v-if="fileLibrary.fileInfo.fileSize">
            {{ FileUtils.formatFileSize(fileLibrary.fileInfo.fileSize) }}
          </div>
        </div>
      </div>
      <div class="file-actions">
        <a-popconfirm cancel-text="插件移除"
                      position="lt"
                      ok-text="远程删除"
                      @cancel="handleFileDelete(fileLibrary, false)"
                      @ok="handleFileDelete(fileLibrary, true)">
          <template #content>
            <span class="u-font-size-smail">请选择删除方式, 放弃删除点击外部区域</span>
          </template>
          <a-button type="text"
                    size="mini"
                    status="danger">
            删除
          </a-button>
        </a-popconfirm>
      </div>
    </div>
    <div>
      <a-tabs size="small" type="capsule"
              :default-active-key="0"
              style="width: 100%;"
              :position="'top'"
              lazy-load>
        <a-tab-pane v-for="(uploadInfo, index) in fileLibrary.fileUploadInfoList"
                    :key="index"
                    :title="storageSourceStore.getStorageSourceById(uploadInfo.storageId)?.storageName || '已删除'">
          <div v-for="(urlItem, i) in fileLibraryConvertUrlItemList(fileLibrary, index)"
               :key="i"
               class="u-fx u-mb6">
            <a-input size="small" :model-value="urlItem.url" readonly>
              <template #prefix>
                {{urlItem.name}}
              </template>
            </a-input>
            <a-button size="small" @click="() => handleCopyText(urlItem.url)">
              <template #icon>
                <iconpark-icon name="copy" style="padding-top: 6px" />
              </template>
              复制
            </a-button>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<style scoped lang="less">

.upload-file-item {
  padding: 12px;
  margin-bottom: 8px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    background: var(--u-hover-color);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.1);
  }
}

// 文件信息
.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  .file-details {
    .file-name {
      font-size: 14px;
      font-weight: 500;
      color: var(--color-text);
      margin-bottom: 4px;
    }

    .file-size {
      font-size: 12px;
      color: #86909c;
    }
  }
}

:deep(.arco-trigger-content) {
  padding: 0;
}
</style>
