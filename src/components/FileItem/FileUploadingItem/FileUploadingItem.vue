<script setup lang="ts">

import FileUtils from '@/utils/FileUtils.ts'
import FileIconPreview from '@/components/FilePreview/FileIconPreview.vue'
import type { FileUpdateInfoItem } from '@/@types'
import type { CascaderNode } from '@arco-design/web-vue/es/cascader/interface'
import { Message } from '@arco-design/web-vue'
import { useUploadFileListStore } from '@/stores'
import { ref, watch } from 'vue'
import { useEventListener } from '@vueuse/core'

const props = defineProps<{
  data: FileUpdateInfoItem;
  selectUploadSourceOptions: CascaderNode[];
}>();

const uploadFileListStore = useUploadFileListStore();

const fileItem = ref(props.data);

useEventListener(window,`fileItemChange::${props.data.id}`, () => {
  const uploadFileItem = uploadFileListStore.getUploadFileItem(props.data.id)
  if (uploadFileItem) {
    fileItem.value = uploadFileItem;
  }
});

// 重新上传
function handleReloadUpload(uploadFileItem: FileUpdateInfoItem) {
  uploadFileListStore.reloadUploadFileItem(uploadFileItem)
}

function handleRemoveUploadItem(uploadFileItem: FileUpdateInfoItem) {
  uploadFileListStore.removeUploadItem(uploadFileItem);
  Message.success("移除成功");
}

</script>

<template>
  <div v-if="fileItem"
       class="u-fx u-f-between u-fac upload-file-item">
    <div class="file-info">
      <div class="file-preview">
        <FileIconPreview :content="fileItem.filePath ? `file://${fileItem.filePath}` : fileItem.content!"
                         :file-suffix="fileItem.fileSuffix" />
      </div>
      <div class="file-details">
        <div class="file-name">{{ fileItem.fileName }}</div>
        <div class="file-size" v-if="fileItem.fileSize">
          {{ FileUtils.formatFileSize(fileItem.fileSize) }}
        </div>
      </div>
    </div>
    <div>
      <a-tag v-if="fileItem.uploadStatus === 'waiting'"
             style="border-radius: 10px">等待上传</a-tag>
      <a-tag v-else-if="fileItem.uploadStatus === 'uploading'"
             style="border-radius: 10px"
             color="blue"
             loading>
        上传中 <span v-if="fileItem.progress" style="padding-left: 2px;">{{fileItem.progress}}%</span>
      </a-tag>
      <a-tag v-else-if="fileItem.uploadStatus === 'failed'"
             style="border-radius: 10px"
             color="red">
        上传失败
      </a-tag>
    </div>
    <div class="u-fx u-gap5" style="flex-direction: column; align-items: end;">
      <a-cascader v-if="fileItem.uploadStatus === 'failed'"
                  :options="selectUploadSourceOptions"
                  v-model:model-value="fileItem.uploadWay"
                  :style="{width:'150px'}"
                  placeholder="选择上传方式"
                  size="mini"
                  allow-search>
      </a-cascader>
      <div v-if="fileItem.uploadStatus === 'failed'"
           class="u-font-size-smail u-fx u-gap10">
        <a-link style="font-size: 12px" status="danger"
                @click="() => handleRemoveUploadItem(fileItem!)">移除</a-link>
        <a-link style="font-size: 12px"
                @click="() => handleReloadUpload(fileItem!)">
          重新上传
        </a-link>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
// 文件信息
.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  .file-details {
    .file-name {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 4px;
    }

    .file-size {
      font-size: 12px;
      color: #86909c;
    }
  }
}
</style>
