<script setup lang="ts">
import { toRefs } from 'vue'
import { useUserSettingStore } from '@/stores'

const { autoCopyUrlFormat, enableFormatUrlData } = toRefs(useUserSettingStore());
</script>

<template>
  <a-select v-model:model-value="autoCopyUrlFormat"
            style="width: 170px"
            allow-clear
            size="small">
    <template #prefix>自动复制</template>
    <a-option v-for="(formatUrlData) in enableFormatUrlData"
              :key="formatUrlData.id"
              :value="formatUrlData.id">
      {{formatUrlData.displayName}}
    </a-option>
  </a-select>
</template>

<style scoped lang="less">

</style>
