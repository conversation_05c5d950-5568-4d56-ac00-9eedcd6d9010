<script lang="ts" setup>
import { ref, withDefaults } from 'vue'
import type { FileUploadItem } from '@/@types'
import { useEventListener } from '@vueuse/core'
import DataUtils from '@/utils/DataUtils.ts'
import fileUtils from '@/utils/FileUtils.ts'

const isDragging = ref(false)
const fileInput = ref(null)

function handleDragOver() {
  isDragging.value = true
}

function handleDragLeave() {
  isDragging.value = false
}


function handleDrop(e: DragEvent) {
  isDragging.value = false
  if (e.dataTransfer) {
    const files = Array.from(e.dataTransfer.files)
    handleFiles(files);
  }
}

function handleFileSelect(e: InputEvent) {
  const files = Array.from((e.target as any).files);
  handleFiles(files as File[])
}

const emits = defineEmits<{
  add: [FileUploadItem[]]
}>();

function handleFiles(files: File[]) {
  emits('add', files.map(content => ({ content, dataType: 'file' })));
}

function handleOpenSelectFile() {
  const res = utools.showOpenDialog({
    title: '选择上传的文件',
    properties: ['multiSelections', 'openFile']
  });
  console.log('handleOpenSelectFile', res)
  if (res && res.length) {
    emits('add', res.map(content => ({ content, dataType: 'filePath' })));
  }
}

useEventListener(document, 'paste', (e) => {
  if (e.clipboardData && e.clipboardData.files && e.clipboardData.files.length > 0) {
    e.preventDefault();
    e.stopPropagation();
    const files = Array.from(e.clipboardData.files)
    handleFiles(files);
  }
})
</script>

<template>
  <div
    class="upload-container"
    @drop.prevent="handleDrop"
    @dragover.prevent="handleDragOver"
    @dragleave.prevent="handleDragLeave"
  >
    <div class="upload-area" :class="{ dragging: isDragging }"
         @click="handleOpenSelectFile">
      <div class="upload-content">
        <iconpark-icon name="upload-one" size="68"></iconpark-icon>
        <input type="file"
               ref="fileInput"
               @change="(e) => handleFileSelect(e as any)"
               class="file-input" />
        <div class="u-font-size-smail" style="padding-top: 10px;">
          支持点击选择文件/拖拽文件/粘贴文件即可上传
        </div>
      </div>
    </div>
  </div>
</template>


<style lang="less" scoped>
.upload-container {
  width: 100%;
  padding: 10px;
  user-select: none;
}

.upload-area {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.dragging {
  border-color: #4080ff;
  background-color: rgba(64, 128, 255, 0.05);
  iconpark-icon[name='upload-one'] {
    color: rgba(64, 128, 255) !important;
  }
}

iconpark-icon[name='upload-one'] {
  color: #cccccc;
}

.file-input {
  display: none;
}
</style>
