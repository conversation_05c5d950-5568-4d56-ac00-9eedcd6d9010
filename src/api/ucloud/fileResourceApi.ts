
/**
 * 根据资源 code
 * @param resourceCode 资源 code
 */
async function getResourceApi(resourceCode: string) {
  return await fetch(`${import.meta.env.VITE_API_BASE_URL}/app/fileResource/getResource?resourceCode=${resourceCode}`)
    .then(res => res.json())
    .then(res => {
      if (res.code !== 0) {
        throw new Error(res.msg);
      }
      return res.data;
    });
}

export interface FileUrlResponse {
  contentType: string;
  fileId: string;
  fileName: string;
  fileSize: string;
  fileSuffix: string;
  previewUrl: string;
}

/**
 * 获取文件下载地址
 * @param fileId
 */
async function getFileUrlApi(fileId: string): Promise<FileUrlResponse> {
  return await fetch(`${import.meta.env.VITE_API_BASE_URL}/app/file-center/getFileUrl?fileId=${fileId}`, {
    headers: {
      'x-app-id': import.meta.env.VITE_APP_ID,
    }
  })
    .then(res => res.json())
    .then(res => {
      if (res.code !== 0) {
        throw new Error(res.msg);
      }
      return res.data;
    });
}

export default {
  getResourceApi,
  getFileUrlApi
}
