import HttpUtils from '@/utils/HttpUtils.ts'


export interface  pluginListGetRequest {
  /**
   * 检索关键字
   */
  keyword?: string;
  pageNo: number;
  pageSize: number;
  /**
   * 插件分组
   */
  pluginGroup?: string;
  /**
   * 插件类型
   */
  pluginType?: string;
}


export interface PluginListGetResponse {
  list: PluginItemList[];
  total: number;
}

export interface PluginItemList {
  pluginAuthor?: string;
  pluginCode?: string;
  pluginDesc?: string;
  pluginFileId?: number;
  pluginLogo?: string;
  pluginName?: string;
  pluginType?: string;
}


/**
 * 插件列表
 * @param params
 */
async function pluginListGetApi(params: pluginListGetRequest): Promise<PluginListGetResponse> {
  return await fetch(`${import.meta.env.VITE_API_BASE_URL}/app/plugin/getList?${HttpUtils.objectToQueryString(params)}`, {
    headers: {
      'x-app-id': import.meta.env.VITE_APP_ID,
    }
  })
    .then(res => res.json())
    .then(res => {
      if (res.code !== 0) {
        throw new Error(res.msg);
      }
      return res.data;
    });
}

export interface CheckUploadPluginParams {
  /**
   * 插件 code
   */
  pluginCode?: null | string;
  /**
   * 插件版本
   */
  pluginVersion?: null | string;
}

/**
 * PluginLibraryUpdateCheckRespVO
 */
export interface PluginLibraryUpdateCheckResponse {
  /**
   * 更新数量
   */
  updateCount?: number | null;
  /**
   * 更新插件列表
   */
  updatePluginList: UploadPluginItem[];
}

/**
 *
 * cn.u.cloud.system.plugin.controller.app.vo.PluginLibraryUpdateCheckRespVO.UploadPluginItem
 *
 * UploadPluginItem
 */
export interface UploadPluginItem {
  /**
   * 插件 code
   */
  pluginCode: string;
  /**
   * 插件版本
   */
  pluginVersion: number;
}

/**
 * 检查更新版本
 * @param params
 */
async function pluginCheckUpdateApi(params: CheckUploadPluginParams[]): Promise<PluginLibraryUpdateCheckResponse> {
  return await fetch(`${import.meta.env.VITE_API_BASE_URL}/app/plugin/checkUpdate`, {
    body: JSON.stringify({
      pluginList: params
    }),
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-app-id': import.meta.env.VITE_APP_ID,
    }
  })
    .then(res => res.json())
    .then(res => {
      if (res.code !== 0) {
        throw new Error(res.msg);
      }
      return res.data;
    });
}


export interface FilePreviewUrlGetResponse {
  /**
   * 文件类型
   */
  contentType?: null | string;
  fileId:  number;
  /**
   * 文件名
   */
  fileName: string;
  /**
   * 文件大小
   */
  fileSize?: null | string;
  /**
   * 文件后缀
   */
  fileSuffix: string;
  /**
   * 预览 URL
   */
  previewUrl: string;
}

/**
 * 插件下载地址
 * @param pluginCode
 */
async function pluginDownloadUrlApi(pluginCode: string): Promise<FilePreviewUrlGetResponse> {
  return await fetch(`${import.meta.env.VITE_API_BASE_URL}/app/plugin/getDownloadUrl?pluginCode=${pluginCode}`, {
    headers: {
      'x-app-id': import.meta.env.VITE_APP_ID,
    }
  })
    .then(res => res.json())
    .then(res => {
      if (res.code !== 0) {
        throw new Error(res.msg);
      }
      return res.data;
    });
}

export default {
  pluginCheckUpdateApi,
  pluginListGetApi,
  pluginDownloadUrlApi
}
