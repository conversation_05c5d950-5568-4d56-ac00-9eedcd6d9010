import { ServiceMessageEvent } from '@xiaou66/interconnect-service'
import { useSceneStore, useStorageSourceStore, useUploadFileListStore, useUserSettingStore } from '@/stores'

export interface IUploadFileParams {
  /**
   * 插件名称
   */
  pluginName?: string,
  uploadWay?: string;
  filePath?: string;
  base64?: string;
}

export default function init() {
  window.linkService.addServiceEventListener('service.upload.file.async', (e: ServiceMessageEvent<IUploadFileParams>) => {
    const params = e.params
    if (!params.base64 && !params.filePath) {
      e.errorResult = {
        code: 10001,
        message: 'base64 和 filePath 不能同时为空'
      };
      return;
    }

    const uploadFileListStore = useUploadFileListStore();
    e.returnResult = null;
    if (params.uploadWay) {
      uploadFileListStore.addUploadFileItem([{
        dataType: params.filePath ? 'filePath' : 'base64',
        content: params.filePath ? params.filePath : params.base64!,
      }], params.uploadWay);
    } else {
      uploadFileListStore.addUploadFileItemByDefault({
        dataType: params.filePath ? 'filePath' : 'base64',
        content: params.filePath ? params.filePath : params.base64!,
      });
    }
  });

  window.linkService.addServiceEventListener('service.upload.file.sync', (e: ServiceMessageEvent<IUploadFileParams>) => {
    console.log('service.upload.file.sync', e);
    const params = e.params
    console.log('111', params)
    if (!params.base64 && !params.filePath) {
      e.errorResult = {
        code: 10001,
        message: 'base64 和 filePath 不能同时为空'
      };
      return;
    }
    const uploadFileListStore = useUploadFileListStore();
    let result
    if (params.uploadWay) {
      result = uploadFileListStore.addUploadFileItemSync({
        dataType: params.filePath ? 'filePath' : 'base64',
        content: params.filePath ? params.filePath : params.base64!,
      }, params.uploadWay)
    } else {
      result = uploadFileListStore.addUploadFileItemSync({
        dataType: params.filePath ? 'filePath' : 'base64',
        content: params.filePath ? params.filePath : params.base64!,
      })
    }
    result.then((res) => {
      console.log('res', res);
      if (res && res.fileUploadInfoList && res.fileUploadInfoList.length > 0) {
        const { autoCopyUrlFormat } = useUserSettingStore()
        const obj = {
          url: res.fileUploadInfoList[0].url,
          autoCopy: !!autoCopyUrlFormat
        }
        e.returnResult = obj;
      } else {
        e.errorResult = {
          code: 10002,
          message: '上传失败'
        }
        // throw Error("上传失败");
      }
    })
  });


  window.linkService.addServiceEventListener('service.info.getUploadWayList', (e: ServiceMessageEvent) => {
    const storageSourceStore = useStorageSourceStore();
    const sceneStore = useSceneStore()
    const storageSourceList = storageSourceStore.storageSourceList;
    const sceneInfoList = sceneStore.sceneInfoList;
    e.returnResult = [
      {
        label: '上传场景',
        value: 'uploadScene',
        children: sceneInfoList
          .filter(item => sceneStore.getSceneDetailItem(item.id))
          .map(item => ({ label: item.sceneName, value: item.id})),
      },
      {
        label: '存储源',
        value: 'storageSource',
        children: storageSourceList
          .filter(item => utools.dbCryptoStorage.getItem(`storageSource/${item.id}`))
          .map(item => ({label: item.storageName, value: item.id})),
      }
    ];
  });
}
