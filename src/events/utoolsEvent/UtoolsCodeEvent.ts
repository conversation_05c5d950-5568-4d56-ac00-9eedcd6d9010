import type { Router } from "vue-router";

export interface PluginEnterEvent {
  code: string;
  type: string;
  payload: any;
  option: any
}

export class UtoolsCodeEvent<T = Record<string, any>> extends CustomEvent<any> {
  public pluginEnterParams: PluginEnterEvent;
  public router: Router;
  public params: T = {} as T;

  private static PARAMS_SPLIT = "?";

  constructor(pluginEnterParams: PluginEnterEvent, router: Router) {
    super(pluginEnterParams.code.split(UtoolsCodeEvent.PARAMS_SPLIT)[0]);
    this.pluginEnterParams = pluginEnterParams;
    this.router = router;
    this.handleEventCode(pluginEnterParams);
  }

  dispatch() {
    window.dispatchEvent(this);
  }

  public handleEventCode(data: PluginEnterEvent) {
    const { code } = data;

    const codes = code.split(UtoolsCodeEvent.PARAMS_SPLIT);
    if (codes.length > 2) {
      throw Error(`code 错误 ${UtoolsCodeEvent.PARAMS_SPLIT} 仅可出现 1 或 0 次`);
    }
    if (codes.length > 1) {
      if (codes.includes('.')) {
        throw Error(`code 错误 ${UtoolsCodeEvent.PARAMS_SPLIT} 仅可以出现在.后面`);
      }
    }

    if (codes.length > 1 && codes[1].includes("=")) {
      const params = new URLSearchParams(codes[1]);
      this.params = Array.from(params.entries()).reduce((acc, [key, value]) => {
        acc[key] = value as any;
        return acc;
      }, {} as any) as T;
    } else {
      this.params = {
        [codes[1]]: true
      } as T
    }
  }

  public hasParamKey(key: string) {
    // @ts-ignore
    return Object.keys(this.params).includes(key);
  }

  public getParamsKey(key: string) {
    // @ts-ignore
    return this.params[key] as any;
  }

}
