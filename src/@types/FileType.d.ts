export type FileUpdateDataType  = 'file' | 'filePath' | 'base64';
export interface FileUploadItem {
  dataType: FileUpdateDataType;
  content: File | string;
}
export interface FileUpdateInfoItem {
  id: string;

  /**
   * 文件路径
   */
  filePath: string;

  /**
   * 放 base64 的
   */
  content?: string;


  /**
   * 文件名称
   */
  fileName: string;

  /**
   * 文件后缀
   */
  fileSuffix: string;

  /**
   * 是否临时文件, 即 base64 文件
   */
  tempFile?: boolean;

  /**
   * 文件大小
   */
  fileSize: number;

  /**
   * 场景名称
   */
  sceneName?: string;

  /**
   * 上传方式
   */
  uploadWay: string;

  /**
   * 上传状态
   */
  uploadStatus: 'waiting' | 'uploading' | 'failed';

  progress?: number;
}
