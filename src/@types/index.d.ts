import nodeFs from 'fs'
import Path from 'node:path'
import http from 'http'
import https from 'node:https'
import Buffer from 'node:buffer'

import net from 'net';
import type * as zip from 'cross-zip'
import type { StoragePlugInManager } from '@/plugin/StoragePlugin'
import type { InterconnectService } from '@xiaou66/interconnect-service'
import type PlugInUtils from '@/utils/PlugInUtils.ts';
import { clipboard as Clipboard, nativeImage as NativeImage } from "electron";

export type * from './FileType.d.ts'
export type Platform = 'macOS' | 'linux' | 'win32'

declare global {
  interface Window {
    nativeImage: NativeImage,
    clipboard: typeof Clipboard,
    api: Record<string, any>
    fs: typeof nodeFs
    path: typeof Path
    http: typeof http
    https: typeof https
    net: typeof net
    nodeRequire: NodeRequire
    nodeBuffer: typeof Buffer
    zip: typeof zip
    storagePlugInManager: StoragePlugInManager,
    linkService:  InterconnectService,
    plugInUtils: PlugInUtils,
  }
}
