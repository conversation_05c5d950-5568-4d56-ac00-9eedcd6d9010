import { defineStore } from "pinia";
import { computed, ref } from 'vue'
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks.ts'
import FileConstants from '@/constant/FileConstants.ts'



export interface IFormatUrlData {
  id: string;
  displayName: string;
  format: string;
  enable: boolean;
  disableDelete?: boolean;
}
export const useUserSettingStore = defineStore('UserSettingStore', () => {

  // 用户选择的上传策略, 可能是场景或者存储源
  const currentSelectUploadId = useUtoolsDbStorage<string>('currentSelectUploadId', '');
  const serviceEnableStatus = useUtoolsDbStorage<boolean>('serviceEnableStatus', false);
  const pluginAutoUpdate = useUtoolsDbStorage<boolean>('pluginAutoUpdate', true);
  const pluginTempFilename = useUtoolsDbStorage('pluginTempFilename', FileConstants.DEFAULT_PLUGIN_TEMP_FILE_NAME);
  const autoCopyUrlFormat = useUtoolsDbStorage('autoCopyUrlFormat', '');
  const uploadPageViewMode = useUtoolsDbStorage<'list', 'image'>('uploadPageViewMode', 'list');
  const uploadTaskConcurrency = useUtoolsDbStorage<number>('uploadTaskConcurrency', 1);


  const formatUrlData = useUtoolsDbStorage<IFormatUrlData[]>('formatUrlData', [
    {
      id: 'url',
      displayName: 'URL',
      format: `{url}`,
      enable: true,
      disableDelete: true,
    },
    {
      id: 'md',
      displayName: 'MD',
      format: `![{filename}]({url})`,
      enable: true,
    },
    {
      id: 'html',
      displayName: 'HTML',
      format: `<img src="{url}" alt="{filename}" title="{filename}" />`,
      enable: false,
    },
    {
      id: 'bbCode',
      displayName: 'BBCode',
      format: `[img]{url}[/img]`,
      enable: false,
    }
  ]);

  const enableFormatUrlData = computed(() => {
    return formatUrlData.value.filter(item =>  item.enable)
  })
  return {
    currentSelectUploadId,
    serviceEnableStatus,
    pluginAutoUpdate,
    pluginTempFilename,
    autoCopyUrlFormat,
    uploadPageViewMode,
    formatUrlData,
    enableFormatUrlData,
    uploadTaskConcurrency
  }
});
