import { defineStore } from "pinia";
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks.ts'
import { nanoid } from 'nanoid'
import { cloneDeep } from 'es-toolkit'
import type {IFlowItem, SaveFlowItem} from "@/views/UploadScene/flow";
import { computed } from 'vue'

export interface SceneInfoItem {
  id: string;
  /**
   * 场景名称
   */
  sceneName: string;
  /**
   * 是否启用
   */
  enable: boolean;
}

export interface SceneDetailItem {
  id: string;
  /**
   * 收集盒子
   */
  fileBoxId: string;
  /**
   * 存储源
   */
  storageSourceIds: string[];
}

export const useSceneStore = defineStore('SceneStore', () => {
  const sceneInfoList = useUtoolsDbStorage<SceneInfoItem[]>('sceneInfoList',
    []);

  // 保存场景信息
  function saveSceneInfo(sceneInfoItem: SceneInfoItem) {
    if (!sceneInfoItem.id) {
      sceneInfoItem.id = `scene-${nanoid()}`;
      sceneInfoList.value.unshift(sceneInfoItem);
    } else {
      const index = sceneInfoList.value
        .findIndex(item => item.id === sceneInfoItem.id)
      if (index >= 0) {
        sceneInfoList.value.splice(index, 1, sceneInfoItem);
      }
    }

    return sceneInfoItem.id;
  }

  function getSceneInfoById(id: string): SceneInfoItem | undefined {
    return sceneInfoList.value.find(item => item.id === id);
  }

  // 保存场景详情
  function saveSceneDetail(detailItem: SceneDetailItem) {
    if (!detailItem.id) {
      throw Error('场景 id 没有传递无法进行保存');
    }

    utools.dbStorage.setItem(`sceneConfig/${detailItem.id}`, cloneDeep(detailItem));
  }



  function getAllSceneDetailList() {
    return utools.db.allDocs('sceneConfig')
      .map(item => item.value) as SceneDetailItem[];
  }

  function getSceneDetailItem(id: string): SceneDetailItem | undefined {
    return utools.dbStorage.getItem(`sceneConfig/${id}`);
  }
  function saveSceneFlowDetail(flowItem: SaveFlowItem) {
    if (!flowItem.id) {
      throw Error('场景 id 没有传递无法进行保存');
    }
    if (!flowItem.createAt) {
      flowItem.createAt = Date.now();
    }
    flowItem.updateAt = Date.now();
    utools.dbStorage.setItem(`sceneFlowConfig/${flowItem.id}`, cloneDeep(flowItem))
  }

  function getSceneFlowDetail(id: string): IFlowItem | undefined {
    return utools.dbStorage.getItem(`sceneFlowConfig/${id}`);
  }

  function removeScene(id: string) {
    utools.dbStorage.removeItem(`sceneConfig/${id}`);
    const index = sceneInfoList.value.findIndex(item => item.id === id);
    if (index >= 0) {
      sceneInfoList.value.splice(index, 1);
    }
  }

  // 删除内场景内配置存储源, 给存储源删除使用
  function removeStorageSource(storageSourceId: string) {
    for (const sceneInfo of sceneInfoList.value) {
      const sceneDetail = getSceneDetailItem(sceneInfo.id);
      if (sceneDetail) {
        const index = sceneDetail.storageSourceIds.findIndex(item => storageSourceId === item);
        if (index >= 0) {
          sceneDetail.storageSourceIds.splice(index, 1);
          saveSceneDetail(sceneDetail);
        }
      }
    }
  }
  return {
    sceneInfoList,
    getSceneInfoById,
    saveSceneInfo,
    saveSceneDetail,
    saveSceneFlowDetail,
    getSceneFlowDetail,
    getSceneDetailItem,
    getAllSceneDetailList,
    removeScene,
    removeStorageSource
  }
});
