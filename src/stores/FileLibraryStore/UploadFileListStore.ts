import { defineStore } from "pinia";
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks.ts';
import { nanoid } from 'nanoid'
import {ref, toRefs} from 'vue'
import type { FileUpdateInfoItem, FileUploadItem } from '@/@types'
import FileUploadUtils from '@/utils/FileUploadUtils.ts'
import { useUserSettingStore } from '@/stores'
import { convertToFileInfoItem } from '@/utils/fileInfoParseUtils.ts'
import { queue } from 'async';
import DataUtils from '@/utils/DataUtils.ts'
import { Message } from '@arco-design/web-vue'
import { fileLibraryConvertUrl } from '@/utils/UrlFormatBuilder.ts'

export interface FileLibraryInfo {
  /**
   * 文件名称
   */
  fileName: string;

  /**
   * 文件后缀
   */
  fileSuffix: string;

  /**
   * 文件大小
   */
  fileSize: number;
}

export interface FileUploadInfo {

  /**
   * 存储源 id
   */
  storageId: string;

  /**
   * URL
   */
  url: string;

  /**
   * 额外数据
   */
  extra: any;
}


export interface FileLibraryItem {
  /**
   * id
   */
  id: string;

  /**
   * 分组 id
   */
  fileBoxId: string;

  /**
   * 文件保存实际的 docId
   */
  fileFileBoxDocId?: string;

  /**
   * 场景 id
   */
  sceneId: string;

  /**
   * 上传文件信息
   */
  fileUploadInfoList: FileUploadInfo[];

  /**
   * 文件信息
   */
  fileInfo: FileLibraryInfo;

  /**
   * 标签分组 id
   */
  tags?: string[];

  /**
   * 创建时间
   */
  createTime: number;
}

// 文件上传
async function doUploadFile(uploadFile: FileUpdateInfoItem): Promise<FileLibraryItem | undefined> {
  const uploadFileListStore = useUploadFileListStore();
  const element = uploadFileListStore.uploadFileList
    .find(i => i.id === uploadFile.id);
  if (!element) {
    return;
  }
  element.uploadStatus = 'uploading';
  const fileItem: FileLibraryItem = {
    id: uploadFile.id,
    fileBoxId: '',
    sceneId: '',
    fileInfo: {
      fileName: uploadFile.fileName,
      fileSize: uploadFile.fileSize,
      fileSuffix: uploadFile.fileSuffix,
    },
    fileUploadInfoList: [],
    createTime: Date.now(),
  };


  // 处理 base 64 文件
  if (!uploadFile.filePath && uploadFile.content) {
    const base64Data = uploadFile.content.replace(/^data:([A-Za-z-+/]+);base64,/, '')
    const buffer = window.nodeBuffer.Buffer.from(base64Data, 'base64');
    const filePath = window.path.join(DataUtils.getTempsPath(), uploadFile.fileName);
    console.log(filePath, buffer)
    window.fs.writeFileSync(filePath, buffer);
    uploadFile.filePath = filePath;
  }

  try {
    if (uploadFile.uploadWay.startsWith('scene-')) {
      // 场景上传
      await FileUploadUtils.uploadSceneFile(uploadFile, fileItem, uploadFile.uploadWay);
    } else {
      // 存储源上传
      const fileUploadInfo = await FileUploadUtils.uploadStorageSourceFile(uploadFile, uploadFile.uploadWay);
      if (fileUploadInfo) {
        fileItem.fileUploadInfoList.push(fileUploadInfo);
      }
    }

  }catch (e) {
    console.error(e);
  }

  if (fileItem.fileUploadInfoList.length > 0) {
    // 上传成功: 至少有一个上传成功
    uploadFileListStore.addFileLibraryItem(fileItem);
    const index = uploadFileListStore.uploadFileList.findIndex(i => i.id === uploadFile.id);
    uploadFileListStore.uploadFileList.splice(index, 1);
    // 复制文件路径
    const { autoCopyUrlFormat, enableFormatUrlData } = useUserSettingStore()
    // 自定复制实现
    if (autoCopyUrlFormat) {
      const formatUrlData = enableFormatUrlData
        .find(item => item.id === autoCopyUrlFormat);
      if (formatUrlData) {
        const formatUrl = fileLibraryConvertUrl(fileItem, formatUrlData.format)
        utools.copyText(formatUrl);
      }
      Message.success("复制成功");
    }
    if (uploadFile.tempFile) {
      // 如果是临时文件删除临时文件夹
      window.fs.rmSync(uploadFile.filePath);
    }
  } else {
    element.uploadStatus ='failed';
  }

  return fileItem;
}

export const useUploadFileListStore = defineStore('FileLibraryStore', () => {
  const uploadFileHistoryList = useUtoolsDbStorage<FileLibraryItem[]>(`uploadFileHistoryList`,
    []);

  function addFileLibraryItem(fileItem: FileLibraryItem) {
    if (!fileItem.id) {
      fileItem.id = nanoid();
    }
    if (uploadFileHistoryList.value.length === 100) {
      uploadFileHistoryList.value.pop();
    }
    uploadFileHistoryList.value.unshift(fileItem);
  }

  const {  uploadTaskConcurrency } = useUserSettingStore();
  const uploadFileListQueue = queue((task: FileUpdateInfoItem, callback) => {
    doUploadFile(task).then(() => {
      callback();
    });
  }, uploadTaskConcurrency);
  // uploadFileListQueue.drain(function() {
  //   console.log('all items have been processed');
  // });

  function getPageUploadFileHistoryList(page = 1, pageSize = 20) {
    const start = (page - 1) * pageSize;
    console.log(start, start + pageSize, uploadFileHistoryList.value.slice(start, start + pageSize))
    return uploadFileHistoryList.value.slice(start, start + pageSize);
  }

  function removeFileHistoryItemById(id: string) {
    const index = uploadFileHistoryList.value.findIndex(item => item.id === id)
    if (index > -1) {
      uploadFileHistoryList.value.splice(index, 1);
    }
  }

  const uploadFileList = ref<FileUpdateInfoItem[]>([]);

  const userSettingStore = useUserSettingStore();


  function getUploadFileItem(id: string) {
    return uploadFileList.value.find(item => item.id === id);
  }

  function uploadUploadFileItem(id: string, key: keyof FileUpdateInfoItem, value: any) {
    const index = uploadFileList.value.findIndex(item => item.id === id);
    uploadFileList.value.splice(index, 1, {
      ...uploadFileList.value[index],
      [key]: value
    });
  }
  // 添加上传项
  async function addUploadFileItemByDefault(...fileUploadFileList: FileUploadItem[]) {
    await addUploadFileItem(fileUploadFileList, userSettingStore.currentSelectUploadId)
  }

  // 添加上传项
  async function addUploadFileItem(fileUploadFileList: FileUploadItem[], uploadWay: string) {
    const fileUpdateInfoItems = await convertToFileInfoItem(fileUploadFileList, uploadWay);
    uploadFileList.value.unshift(...fileUpdateInfoItems);
    console.log(uploadFileListQueue);
    uploadFileListQueue.push(fileUpdateInfoItems);
  }

  async function addUploadFileItemSync(fileUploadFileList: FileUploadItem, uploadWay?: string) {
    uploadWay = uploadWay || userSettingStore.currentSelectUploadId;
    const fileUpdateInfoItems = await convertToFileInfoItem([fileUploadFileList], uploadWay);
    uploadFileList.value.unshift(...fileUpdateInfoItems);
    return await doUploadFile(fileUpdateInfoItems[0])
  }



  function reloadUploadFileItem(fileUploadFile: FileUpdateInfoItem) {
    const uploadFileItem = uploadFileList.value
      .find(item => item.id === fileUploadFile.id);
    console.log(uploadFileItem);
    if (uploadFileItem) {
      uploadFileItem.uploadStatus = 'waiting';
      uploadFileListQueue.push(uploadFileItem).then(r => {});
    }
  }

  function removeUploadItem(fileUploadFile: FileUpdateInfoItem) {
    const index = uploadFileList.value.findIndex(item => item.id === fileUploadFile.id);
    uploadFileList.value.splice(index, 1);
  }

  return {
    uploadFileHistoryList,
    addFileLibraryItem,
    removeUploadItem,
    getPageUploadFileHistoryList,
    removeFileHistoryItemById,
    getUploadFileItem,
    uploadUploadFileItem,
    addUploadFileItem,
    addUploadFileItemByDefault,
    reloadUploadFileItem,
    uploadFileList,
    addUploadFileItemSync
  }
});
