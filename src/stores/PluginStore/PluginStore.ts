import { defineStore } from "pinia";
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks.ts'

export const usePluginStore = defineStore('PluginStore', () => {
  const pluginManualInstallPathList = useUtoolsDbStorage<string[]>(`pluginManualInstallPathList/${utools.getNativeId()}`,
    []);

  function loadLocalManualPlugin(pluginPath: string) {
    pluginManualInstallPathList.value.push(pluginPath);
    window.storagePlugInManager.loadPlugin(pluginPath);
  }

  async function allInstallManualPlugins() {
    console.log('allInstallManualPlugins')
    const deletePathList = new Set();
    for (const pluginPath of pluginManualInstallPathList.value) {
      try {
        await window.storagePlugInManager.loadPlugin(pluginPath);
      } catch (e) {
        deletePathList.add(pluginPath);
        console.error(e);
      }
    }
    // 移除手动插件错误目录
    const array = pluginManualInstallPathList.value;
    pluginManualInstallPathList.value = Array.from(new Set([...array].filter(x => !deletePathList.has(x))));
  }

  function isManualPlugin(pluginPath: string) {
    console.log(pluginPath,  pluginManualInstallPathList.value)
    console.log(pluginManualInstallPathList.value.includes(pluginPath));
    return pluginManualInstallPathList.value.includes(pluginPath)
  }

  function removeManualPlugin(pluginPath: string) {
    pluginManualInstallPathList.value.splice(pluginManualInstallPathList.value.indexOf(pluginPath), 1);
    console.log('removeManualPlugin', pluginManualInstallPathList.value)
  }

  allInstallManualPlugins()
    .then(() => {
      console.log('手动加载插件完成 ✅')
    });

  return {
    pluginManualInstallPathList,
    loadLocalManualPlugin,
    isManualPlugin,
    removeManualPlugin
  }
});
