import { defineStore } from "pinia";
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks.ts'
import { nanoid } from 'nanoid'
import { ref, watch } from 'vue'
import { useSceneStore, useUserSettingStore } from '@/stores'

export interface StorageSourceItem {
  id: string;
  storageName: string;
  /**
   * 存储插件 code
   */
  storagePluginCode: string;
}


export const useStorageSourceStore = defineStore('StorageSourceStore', () => {
  const storageSourceList = useUtoolsDbStorage<StorageSourceItem[]>(`storageSourceList`,
    []);

  const storageSourceMap = ref<Record<string, StorageSourceItem>>({});


  function loadStorageSourceMap() {
    storageSourceMap.value = storageSourceList.value.reduce((acc, cur) => {
      acc[cur.id] = cur;
      return acc;
    }, {} as any);
    console.log('storageSourceMap', storageSourceMap.value);
  }
  watch(() => storageSourceList.value, (newList) => {
    loadStorageSourceMap();
  }, {
    deep: true,
  });

  function saveStorageSource(sourceItem: StorageSourceItem) {
    if (!sourceItem.id) {
      sourceItem.id = nanoid();
    }
    const index = storageSourceList.value.findIndex(item => item.id === sourceItem.id);
    if (index >= 0) {
      storageSourceList.value.splice(index, 1, sourceItem);
    } else {
      storageSourceList.value.unshift({
        ...sourceItem,
      });
    }
    return sourceItem.id;
  }

  function getStorageSourceById(id: string): StorageSourceItem {
    return storageSourceMap.value[id];
  }

  function deleteStorageSource(id: string) {
    const sceneStore = useSceneStore();
    sceneStore.removeStorageSource(id);
    const index = storageSourceList.value.findIndex(item => item.id === id);
    if (index >= 0) {
      storageSourceList.value.splice(index, 1);

      const userSettingStore = useUserSettingStore()
      if (userSettingStore.currentSelectUploadId === id) {
        if (storageSourceList.value.length) {
          userSettingStore.currentSelectUploadId = storageSourceList.value[0].id;
        } else {
          userSettingStore.currentSelectUploadId = '';
        }
      }
    }
  }


  loadStorageSourceMap();
  return {
    storageSourceList,
    saveStorageSource,
    getStorageSourceById,
    deleteStorageSource,
  }
});
