import { defineStore } from 'pinia'
import { useUtoolsDbStorage } from '@/hooks/utoolsHooks.ts'
import { nanoid } from 'nanoid'
import { type FileLibraryItem, useSceneStore } from '@/stores'
import { cloneDeep } from 'es-toolkit'

export interface FileBoxCatalog {
  total: number
  docIds: string[]
}
const defaultFileBoxCatalogValue: FileBoxCatalog = {
  total: 0,
  docIds: [],
}
export interface FileBoxInfoItem {
  id: string
  /**
   * 文件盒子名称
   */
  fileBoxName: string
}

export const useFileBoxStore = defineStore('FileBoxStore', () => {
  const fileBoxInfoList = useUtoolsDbStorage<FileBoxInfoItem[]>('fileBoxInfoList', [])

  // 保存场景信息
  function saveFileBoxInfo(sceneInfoItem: FileBoxInfoItem) {
    if (!sceneInfoItem.id) {
      sceneInfoItem.id = nanoid()
      fileBoxInfoList.value.unshift(sceneInfoItem)
    } else {
      const index = fileBoxInfoList.value.findIndex((item) => item.id === sceneInfoItem.id)
      if (index >= 0) {
        fileBoxInfoList.value.splice(index, 1, sceneInfoItem)
      }
    }
    return sceneInfoItem.id
  }

  // 添加文件到文件盒子
  function addFileLibraryItem(fileLibrary: FileLibraryItem) {
    if (!fileLibrary.fileBoxId) {
      return
    }
    const fileBoxId = fileLibrary.fileBoxId
    const fileBoxCatalog =
      utools.dbStorage.getItem(`fileBoxCatalog/${fileBoxId}`) ||
      cloneDeep(defaultFileBoxCatalogValue)
    if (!fileBoxCatalog.docIds.length) {
      fileBoxCatalog.docIds.push(`fileBoxCatalog/${fileBoxId}/1`)
    }
    let lastDocId = fileBoxCatalog.docIds[fileBoxCatalog.docIds.length - 1]
    let fileLibraryList = utools.dbStorage.getItem(lastDocId) || []
    if (fileLibraryList.length >= 100) {
      lastDocId = `fileBoxCatalog/${fileBoxId}/${fileBoxCatalog.docIds.length + 1}`
      fileBoxCatalog.docIds.push(lastDocId)
      fileLibraryList = []
    }

    fileLibrary.fileFileBoxDocId = lastDocId
    fileLibraryList.push(fileLibrary)
    fileBoxCatalog.total += 1
    console.log('lastDocId', lastDocId)
    utools.dbStorage.setItem(lastDocId, fileLibraryList)
    utools.dbStorage.setItem(`fileBoxCatalog/${fileBoxId}`, fileBoxCatalog)
  }

  // 获取文件盒子列表
  function getFileLibraryList(
    fileBoxId: string,
    page: number,
    pageSize = 20,
  ): { total: number; list: FileLibraryItem[] } {
    const fileBoxCatalog =
      utools.dbStorage.getItem(`fileBoxCatalog/${fileBoxId}`) ||
      cloneDeep(defaultFileBoxCatalogValue)

    const result: FileLibraryItem[] = []
    const total = fileBoxCatalog.total

    if (total === 0) {
      return { total: 0, list: [] }
    }

    // 计算需要从哪些文档中获取数据
    const startIndex = total - page * pageSize
    const endIndex = Math.min(total - (page - 1) * pageSize, total)

    // 从后向前遍历文档ID
    for (let i = fileBoxCatalog.docIds.length - 1; i >= 0; i--) {
      const docId = fileBoxCatalog.docIds[i]
      const fileLibraryList = utools.dbStorage.getItem(docId) || []

      // 倒序遍历当前文档中的项目
      for (let j = fileLibraryList.length - 1; j >= 0; j--) {
        const currentIndex = i * 100 + j
        if (currentIndex >= startIndex && currentIndex < endIndex) {
          result.push(fileLibraryList[j])
        }
        if (currentIndex < startIndex) {
          break
        }
      }

      if (result.length >= pageSize) {
        break
      }
    }

    return {
      total,
      list: result,
    }
  }

  // 删除文件
  function removeFileLibrary(fileLibrary: FileLibraryItem) {
    if (fileLibrary.fileFileBoxDocId) {
      const fileLibraryItems: FileLibraryItem[] =
        utools.dbStorage.getItem(fileLibrary.fileFileBoxDocId) || []
      const index = fileLibraryItems.findIndex((item) => item.id === fileLibrary.id)
      if (index >= 0) {
        fileLibraryItems.splice(index, 1)
        const key = `fileBoxCatalog/${fileLibrary.fileBoxId}`
        const fileBoxCatalog = utools.dbStorage.getItem(key) as FileBoxCatalog
        fileBoxCatalog.total -= 1
        utools.dbStorage.setItem(key, fileBoxCatalog)
        utools.dbStorage.setItem(fileLibrary.fileFileBoxDocId, fileLibraryItems)
      }
    }
  }

  function getFileBoxCatalog(fileBoxId: string) {
    const key = `fileBoxCatalog/${fileBoxId}`
    return utools.dbStorage.getItem(key) as FileBoxCatalog
  }

  async function removeFileBox(fileBoxId: string) {
    const key = `fileBoxCatalog/${fileBoxId}`;
    utools.db.allDocs(key).map(item => {
      utools.dbStorage.removeItem(item._id);
    });
    const index = fileBoxInfoList.value
      .findIndex((item) => item.id === fileBoxId);
    if (index >= 0) {
      fileBoxInfoList.value.splice(index, 1);
    }
    const sceneStore = useSceneStore();
    const sceneDetailItems = sceneStore.getAllSceneDetailList()
      .filter(item => item.fileBoxId === fileBoxId)
      .map(item => {
        item.fileBoxId = '';
        return item;
      });

    for (const sceneDetailItem of sceneDetailItems) {
      sceneStore.saveSceneDetail(sceneDetailItem);
    }
  }
  return {
    fileBoxInfoList,
    saveFileBoxInfo,
    addFileLibraryItem,
    getFileLibraryList,
    removeFileLibrary,
    getFileBoxCatalog,
    removeFileBox
  }
})
