import { ref, type Ref, shallowRef, toRaw, watch } from 'vue'
import { isArray, isObject } from '@/utils/jsUtil'
import { mergeWith } from 'es-toolkit'

export interface UseUtoolsDbOptions<T, E> {
  templateValue?: E,
  /**
   * 区分平台
   */
  platform?: boolean;
  flush?: 'pre' | 'post' | 'sync';
  deep?: boolean;
  /**
   * 是否使用 shallowRef 包装对象
   */
  isShallowRef?: boolean;
  onError?(e: any): void;
  initData?: (data: T) => void;
}

/**
 * 生成保存的 key
 * @param key
 * @param platform
 */
function generateSaveKey(key: string, platform: boolean) {
  const keys = [key];
  if (platform) {
    if (utools.isWindows()) {
      keys.push('windows');
    } else if (utools.isMacOS()) {
      keys.push('macOS');
    } else if (utools.isLinux()) {
      keys.push('linux');
    }
  }
  return keys.join('/');
}

/**
 * 异步对象存储
 */
export function useUtoolsDbStorage<T extends (string | number | boolean | object | null) = any,
  E extends (string | number | boolean | object | null) = any>(
  key: string,
  initialValue: T,
  options: UseUtoolsDbOptions<T, E> = {} as any,
): Ref<T> {
  const {
    templateValue = undefined,
    platform = false,
    flush = 'pre',
    deep = true,
    isShallowRef,
    onError = (e: any) => {
      console.error(e);
    },
    initData = (data: any) => {}
  } = options;

  key = generateSaveKey(key, platform);


  const sourceValue = window.utools ?  utools.dbStorage.getItem(key) : null;

  let data = null;
  if (isObject(initialValue)) {
     data = (isShallowRef ? shallowRef : ref)((typeof sourceValue === 'undefined' || sourceValue === null)
       ? initialValue
       : mergeWith(sourceValue, initialValue, (objValue: any) =>  objValue)) as Ref<T>
  } else if (isArray(initialValue) && templateValue) {
    data = (isShallowRef ? shallowRef : ref)((typeof sourceValue === 'undefined' || sourceValue === null)
      ? initialValue
      : sourceValue.map((item: any) => mergeWith(item, templateValue, (objValue: any) => objValue))) as Ref<T>
  } else {
    data = (isShallowRef ? shallowRef : ref)((typeof sourceValue === 'undefined' || sourceValue === null)
      ? initialValue
      : sourceValue) as Ref<T>
  }

  if (sourceValue == null) {
    initData(data.value);
  }

  watch(
    data,
    (val) => {
      try {
        console.log('变更', val, data.value)
        if (data.value == null) {
          utools.dbStorage.removeItem(key)
        } else {
          utools.dbStorage.setItem(key, toRaw(data.value))
        }
      } catch (e) {
        onError(e)
      }
    },
    {
      flush,
      deep,
    },
  )

  return data as Ref<T>
}
