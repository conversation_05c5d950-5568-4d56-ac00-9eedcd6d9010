import { ref } from 'vue'

const mediaQueryList = window.matchMedia('(prefers-color-scheme: dark)');

type themeType ='default' | 'dark'| 'auto';

export interface UseThemeOptions {
  setDefaultTheme: () => void;
  setDarkTheme: () => void;
  getUserThemeConfig?: () => themeType;
}
const currentTheme = ref<themeType>('default');
export function useTheme(options: UseThemeOptions) {
  function mediaQueryListChange(event: MediaQueryListEvent) {
    if (options.getUserThemeConfig) {
      const userConfig = options.getUserThemeConfig();
      if (userConfig !== 'auto') {
        if (userConfig === 'dark') {
          options.setDarkTheme();
          currentTheme.value = 'dark';
        } else if (userConfig === 'default') {
          options.setDefaultTheme();
          currentTheme.value = 'default';
        }
        return;
      }
    }
    if (event.matches) {
      options.setDarkTheme();
      currentTheme.value = 'dark';
    } else {
      options.setDefaultTheme();
      currentTheme.value = 'default';
    }
  }
  function themeLoad() {
    mediaQueryList.addEventListener('change', mediaQueryListChange);
    // 手动触发一下 change 事件
    mediaQueryList.dispatchEvent(new MediaQueryListEvent('change', mediaQueryList));
  }

  function themeRefresh() {
    // 手动触发一下 change 事件
    mediaQueryList.dispatchEvent(new MediaQueryListEvent('change', mediaQueryList));
  }

  themeLoad();
  console.log('mediaQueryList', mediaQueryList)
  return {
    themeRefresh
  }
}

export function setCurrentTheme(theme: 'default' | 'dark') {
  mediaQueryList.dispatchEvent(new MediaQueryListEvent('change', {
    matches: theme === 'dark',
    media: '(prefers-color-scheme: dark)'
  }));
}

export function getCurrentTheme() {
  return currentTheme.value;
}
