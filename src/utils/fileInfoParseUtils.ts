import type { FileUpdateInfoItem, FileUpdateDataType, FileUploadItem } from '@/@types'
import { nanoid } from 'nanoid'
import DataUtils from '@/utils/DataUtils.ts'
import { useUserSettingStore } from '@/stores'
import { filePathFormat } from '@xiaou66/picture-plugin'
import FileConstants from '@/constant/FileConstants.ts'
import fileUtils from '@/utils/FileUtils.ts'

type FileInfoParseType =  (fileItem: FileUploadItem) => Promise<FileUpdateInfoItem>;

const mineMap: Record<string, string> = {
  'image/png': 'png',
  'image/jpeg': 'jpg',
  'image/gif': 'gif',
  'image/webp': 'webp',
  'image/svg+xml': 'svg',
  'image/x-icon': 'ico',
  'image/bmp': 'bmp',
  'image/tiff': 'tiff',
  'image/avif': 'avif',
  'image/heic': 'heic',
  'image/avcs': 'avcs',
  'image/avci': 'avci',
}

const handle: Record<FileUpdateDataType, FileInfoParseType> = {
  'filePath': async (fileItem: FileUploadItem) => {
    const stats = window.fs.statSync(fileItem.content as string)
    return {
      id: nanoid(),
      fileName: window.path.basename(fileItem.content as string),
      filePath: fileItem.content,
      fileSize: stats.size,
    } as FileUpdateInfoItem;
  },
  'file': async (fileItem: FileUploadItem) => {
    const content = fileItem.content as File;
    // @ts-ignore
    if (content.path) {
      return {
        id: nanoid(),
        fileName: content.name,
        filePath: (content as any).path,
        fileSize: content.size,
      } as FileUpdateInfoItem;
    } else {
      const fileName = `${Date.now()}.${mineMap[content.type] || 'png'}`;
      const tempFilePath = window.path.join(DataUtils.getTempsPath(), fileName);
      window.fs.writeFileSync(tempFilePath, (await fileUtils.blobToBuffer(content)));
      return {
        id: nanoid(),
        fileName,
        filePath: tempFilePath,
        fileSize: content.size,
        tempFile: true,
      } as FileUpdateInfoItem;
    }

  },
  'base64': async (fileItem: FileUploadItem) => {
    const content = fileItem.content as string;
    // @ts-ignore
    const [src, type] = content.match(/^data:(image\/.+);base64,/);
    const { pluginTempFilename } = useUserSettingStore();
    const fileName = filePathFormat(pluginTempFilename || FileConstants.DEFAULT_PLUGIN_TEMP_FILE_NAME, {
      id: nanoid(),
      storageId: nanoid(),
      sceneName: '',
      filePath: '',
      allFileName: '',
      fileName: '',
      // @ts-ignore
      suffix: mineMap[type],
      fileSize: 100,
      file: {} as File,
    })
    return {
      id: nanoid(),
      // @ts-ignore
      fileName,
      filePath: '',
      content: fileItem.content,
      fileSize: 0,
      tempFile: true,
    } as FileUpdateInfoItem;
  }
}

export async function convertToFileInfoItem(fileItemList: FileUploadItem[], uploadWay: string): Promise<FileUpdateInfoItem[]> {
  const res: FileUpdateInfoItem[] = [];
  for (const fileItem of fileItemList) {
    const fileUpdateInfoItem = await handle[fileItem.dataType](fileItem);
    res.push(fileUpdateInfoItem);
  }
  return res.map(item => {
    item.uploadStatus = 'waiting';
    item.uploadWay = uploadWay;
    item.fileSuffix = window.path.extname(item.fileName).replace('.', '');
    return item;
  });
}
