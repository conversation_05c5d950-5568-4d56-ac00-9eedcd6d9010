import { type FileLibraryItem, useUserSettingStore } from '@/stores'

export interface IUrlFormatItem {
  name: string;
  url: string;
}

export function fileLibraryConvertUrlItemList(fileLibraryList: FileLibraryItem, index = 0): IUrlFormatItem[] {
  const obj = [
    {
      label: '文件 url',
      value: '{url}',
      format: (obj: any) => fileLibraryList.fileUploadInfoList[index].url,
    },
    {
      label: '文件名称',
      value: '{filename}',
      format: (obj: any) => fileLibraryList.fileInfo.fileName,
    },
  ];
  const { enableFormatUrlData } = useUserSettingStore();
  return enableFormatUrlData.map(item => {
      let str = item.format;
      obj.forEach(variable => {
        // 获取变量的值
        const value = variable.format(obj) || '';
        // 使用正则表达式替换所有匹配的变量
        const regex = new RegExp(variable.value, 'g'); // 'g' 标志表示全局替换
        str = str.replace(regex, value);
      });
      return {
        name: item.displayName,
        url: str,
      }
    })
}

export function fileLibraryConvertUrl(fileLibraryList: FileLibraryItem, formatValue: string, index = 0) {
  const obj = [
    {
      label: '文件 url',
      value: '{url}',
      format: (obj: any) => fileLibraryList.fileUploadInfoList[index].url,
    },
    {
      label: '文件名称',
      value: '{filename}',
      format: (obj: any) => fileLibraryList.fileInfo.fileName,
    },
  ];
  let str = formatValue;
  obj.forEach(variable => {
    // 获取变量的值
    const value = variable.format(obj) || '';
    // 使用正则表达式替换所有匹配的变量
    const regex = new RegExp(variable.value, 'g'); // 'g' 标志表示全局替换
    str = str.replace(regex, value);
  });
  return str;
}
