function formatFileSize(bytes: number) {
  if (bytes < 0) return 'Invalid size';

  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let index = 0;

  while (bytes >= 1024 && index < units.length - 1) {
    bytes /= 1024;
    index++;
  }

  // 保留两位小数
  return `${bytes.toFixed(2)} ${units[index]}`;
}

/**
 * 创建不存在目录
 * @param dir
 */
function createDirIfAbsent(dir: string) {
  if (!window.fs.existsSync(dir)) {
    window.fs.mkdirSync(dir, { recursive: true });
  }
}

function unZip(filePath: string, outputDir: string, deleteOriginalFile = false) {
  return new Promise((resolve, reject) => {
    console.log('unZip', filePath, outputDir)
    try {
      createDirIfAbsent(outputDir);
    } catch (e) {
      console.error('创建目录失败--', e);
      reject('创建目录失败');
    }
    window.zip.unzipSync(filePath, outputDir);
    if (deleteOriginalFile) {
      window.fs.rmSync(filePath);
    }
    resolve(true);
  });
}

/**
 * Blob转Buffer
 * @param blob {Blob} 内容
 * @return {Promise<Buffer>}
 */
async function blobToBuffer (blob: Blob): Promise<any> {
  return blob.arrayBuffer()
    .then(buffer => window.nodeBuffer.Buffer.from(buffer));
}


export default {
  blobToBuffer,
  formatFileSize,
  unZip,
  createDirIfAbsent
}
