import FileUtils from '@/utils/FileUtils.ts'

function getDataSavePath() {
  return utools.dbStorage.getItem(`settingUserStore/localAppData/${utools.getNativeId()}`)
    || window.path.join(utools.getPath('userData'), 'app', 'picture-bed-plus');
}
function getPluginPath() {
  return window.path.join(getDataSavePath(), 'plugins');
}

function getTempsPath() {
  const tempPath =  window.path.join(getDataSavePath(), 'temps');
  FileUtils.createDirIfAbsent(tempPath);
  return tempPath;
}

export default {
  getDataSavePath,
  getPluginPath,
  getTempsPath,
};
