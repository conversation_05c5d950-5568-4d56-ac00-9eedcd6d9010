import type { FileUpdateInfoItem } from '@/@types'
import {
  type FileLibraryItem,
  type FileUploadInfo,
  type SceneDetailItem,
  type SceneInfoItem,
  useFileBoxStore,
  useSceneStore,
  useStorageSourceStore, useUploadFileListStore
} from '@/stores'
import FileUtils from '@/utils/FileUtils.ts'
import {FlowExecutor} from "@/views/UploadScene/flow";

async function uploadSceneFile(
  uploadFile: FileUpdateInfoItem,
  fileLibrary: FileLibraryItem,
  sceneId: string,
) {
  const sceneStore = useSceneStore()
  const sceneInfo = sceneStore.getSceneInfoById(sceneId);
  if (!sceneInfo) {
    return;
  }

  // 设置场景 id
  fileLibrary.sceneId = sceneId;
  uploadFile.sceneName = sceneInfo.sceneName;
  await FlowExecutor.executor(sceneId, {
    uploadFile,
    fileLibrary,
    sceneInfo,
  });



  // const sceneDetail = sceneStore.getSceneDetailItem(sceneId)
  // if (!sceneDetail || sceneDetail.storageSourceIds.length === 0 || !sceneInfo) {
  //   return
  // }
  //
  // const res = await uploadSceneFileExecute(uploadFile, sceneDetail, sceneInfo)
  // fileLibrary.fileUploadInfoList.push(...res)
  //
  // debugger
  // if (sceneDetail.fileBoxId) {
  //   const fileBoxStore = useFileBoxStore()
  //   fileLibrary.fileBoxId = sceneDetail.fileBoxId
  //   fileBoxStore.addFileLibraryItem(fileLibrary)
  // }
}
/**
 * 上传场景文件
 * @param uploadFile 上传文件信息
 * @param sceneDetail
 * @param sceneInfo
 * @constructor
 */
async function uploadSceneFileExecute(
  uploadFile: FileUpdateInfoItem,
  sceneDetail: SceneDetailItem,
  sceneInfo: SceneInfoItem,
) {
  const storageSourceIds = sceneDetail.storageSourceIds
  uploadFile.sceneName = sceneInfo.sceneName

  return (
    await Promise.all(
      storageSourceIds.map((storageSourceId) => {
        return uploadStorageSourceFile(uploadFile, storageSourceId)
      }),
    )
  ).filter((item) => item) as FileUploadInfo[]
}

/**
 * 存储源上传
 * @param uploadFile 上传文件信息
 * @param storageSourceId 存储 id
 */
async function uploadStorageSourceFile(uploadFile: FileUpdateInfoItem, storageSourceId: string) {
  debugger
  const storageSourceStore = useStorageSourceStore();
  const uploadFileListStore = useUploadFileListStore()
  if (!storageSourceId) {
    throw new Error('选择上传方式')
  }
  const fileSize = uploadFile.fileSize
  const isBigFile = fileSize > 1024 * 1024 * 1024

  const storageSource = storageSourceStore.getStorageSourceById(storageSourceId)
  const loadPlugin = window.storagePlugInManager.getPluginInstance(storageSource.storagePluginCode)
  const res = await loadPlugin.uploadFile({
    id: uploadFile.id,
    allFileName: uploadFile.fileName,
    fileName: window.path.basename(uploadFile.fileName).split('.')[0],
    suffix: window.path.extname(uploadFile.fileName).replace('.', ''),
    sceneName: uploadFile.sceneName || '',
    file: isBigFile ? undefined : (new Blob([window.fs.readFileSync(uploadFile.filePath)]) as File),
    storageId: storageSourceId,
    fileSize,
    filePath: uploadFile.filePath,
  }, {
    onProgress(progress) {
      uploadFileListStore.uploadUploadFileItem(uploadFile.id, 'progress', progress);
      window.dispatchEvent(new CustomEvent(`fileItemChange::${uploadFile.id}`));
    },
  })
  if (!res) {
    return
  }

  return {
    storageId: storageSource.id,
    url: res.url,
    extra: res.extra,
  } as FileUploadInfo
}
export default {
  uploadStorageSourceFile,
  uploadSceneFile,
}
