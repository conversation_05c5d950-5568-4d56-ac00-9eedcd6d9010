import FileUtils from '@/utils/FileUtils.ts'

function objectToQueryString(obj: Record<any, any>) {
  return Object.entries(obj)
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    .join('&');
}

/**
 * 下载网络文件
 * @param url url
 * @param saveAsPath 保存地址
 * @param options
 * @returns {Promise<string>}
 */
function downloadFile(url: string, saveAsPath: string, options: Record<string, any> = {}): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      if (!window.fs.existsSync(window.path.dirname(saveAsPath))) {
        FileUtils.createDirIfAbsent(window.path.dirname(saveAsPath));
      }
    } catch (e) {
      reject(e);
    }
    const fileWrite = window.fs.createWriteStream(saveAsPath);

    const request = url.startsWith('https') ? window.https : window.http;
    request
      .get(url, options, (resp: any) => {
        resp.pipe(fileWrite);
        fileWrite.on('finish', () => {
          fileWrite.close();
          resolve(url);
        });
      })
      .on('error', (err: any) => {
        reject(err);
      });
  });
}

export default {
  objectToQueryString,
  downloadFile
}
