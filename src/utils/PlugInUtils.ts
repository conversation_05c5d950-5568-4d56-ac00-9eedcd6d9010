export default class PlugInUtils {

  private __plugName: string;

  constructor(config: {plugName: string}) {
    this.__plugName = config.plugName;
  }


  /**
   * 获取插件用户数
   */
  async getPlugUserCount(): Promise<number> {
    try {
      const htmlElement = await this.getPlugInfo();
      const userCountStrList = Array.from(htmlElement.querySelectorAll('.swiper-slide>div>div'))
        .filter(elementItem => (elementItem as HTMLElement).innerText.includes('位'))
        .map(elementItem => (elementItem as HTMLElement).innerText.replace('位', '').trim());
      if (userCountStrList && userCountStrList.length) {
        return parseInt(userCountStrList[0]);
      }
    } catch (e) {
      return 0;
    }
    return 0;
  }
  async getPlugInfo() {
    try {
      const parser = new DOMParser();
      return await fetch(`https://u.tools/plugins/detail/${this.__plugName}/`)
        .then(res => res.text())
        .then(res => parser.parseFromString(res, 'text/html'));
    }catch (e) {
      console.error("code: 404, 获取插件信息失败", e);
      throw Error('code: 404, 获取插件信息失败');
    }
  }
}
