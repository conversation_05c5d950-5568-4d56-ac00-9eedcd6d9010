
const typeMap: Record<string, string> = {
  '[object Number]': 'number',
  '[object Boolean]': 'boolean',
  '[object String]': 'string',
  '[object Array]': 'array',
  '[object Object]': 'object',
  '[object Function]': 'function',
  '[object Undefined]': 'undefined',
  '[object Null]': 'null',
}

function isType(value: any, type: string) {
  const toString = Object.prototype.toString;
  return typeMap[toString.call(value)] === type;
}

export function isObject(value: any): value is object {
  return isType(value, 'object');
}

export function isArray <T>(value: any[T]): value is Array<T> {
  return isType(value, 'array');
}

export function isString(value: any): value is string {
  return isType(value, 'string');
}

export function isNumber(value: any): value is number {
  return isType(value, 'number');
}


/**
 * 动态加载脚本
 * @param url 脚本路径
 * @param callback onload 回调
 */
function loadScript(url: string, callback?: () => void) {
  const script = document.createElement("script");
  script.type = "text/javascript";
  script.src = url;
  script.async = true;

  script.onload = function () {
    if (callback) {
      callback();
    }
  };

  document.head.appendChild(script);
}


function camelToSnake(camelCase: string) {
  return camelCase
    .replace(/([A-Z])/g, '_$1') // 在大写字母前加上下划线
    .toLowerCase(); // 转换为小写字母
}

function snakeToCamel(snakeCase: string) {
  return snakeCase
    .toLowerCase() // 将字符串转为小写
    .replace(/_./g, match => match.charAt(1).toUpperCase()); // 替换下划线及其后面的字符
}

/**
 * 随机数
 * @param min 最小值
 * @param max 最大值
 */
function getRandomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}
export default {
  isString,
  isNumber,
  isArray,
  isObject,
  loadScript,
  camelToSnake,
  snakeToCamel,
  getRandomInt
}
