(function(){window.__iconpark__=window.__iconpark__||{};var obj=JSON.parse("{\"1368803\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M11.678 20.271C7.275 21.318 4 25.277 4 30c0 5.523 4.477 10 10 10 .947 0 1.864-.132 2.733-.378M36.055 20.271c4.403 1.047 7.677 5.006 7.677 9.729 0 5.523-4.477 10-10 10-.947 0-1.864-.132-2.732-.378M36 20c0-6.627-5.373-12-12-12s-12 5.373-12 12M17.065 27.881 24 20.924 31.132 28M24 38V24.462\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1368816\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M44 29H4v13h40V29Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path fill=\\\"currentColor\\\" d=\\\"M35.5 38a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z\\\" data-follow-fill=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M4 29 9.038 4.999H39.02l4.98 24\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M19.006 16.026c-2.143 0-4.006 1.486-4.006 3.487C15 22 17.095 23 19.697 23h1.28M29.007 16.026c2.097 0 3.993.973 3.993 3.487C33 22 30.89 23 28.288 23h-1.3M29.007 16.026C29.007 13.042 27.023 11 24 11c-3.023 0-4.994 1.993-4.994 5.026\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M20 23h8\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1368817\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M24 40.833a11.955 11.955 0 0 0 8 3.056c6.627 0 12-5.373 12-12 0-5.301-3.437-9.8-8.204-11.387\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M27.171 27.5c.535 1.359.829 2.84.829 4.39 0 6.627-5.373 12-12 12-6.628 0-12-5.373-12-12 0-5.316 3.455-9.824 8.242-11.4\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M24 27.89c6.627 0 12-5.373 12-12 0-6.628-5.373-12-12-12s-12 5.372-12 12c0 6.627 5.373 12 12 12Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1368818\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"m34 41 10-17L34 7H14L4 24l10 17h20Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M24 29a5 5 0 1 0 0-10 5 5 0 0 0 0 10Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1368837\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"m24.06 10-.036 28M10 24h28\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1368843\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><circle fill=\\\"currentColor\\\" r=\\\"3\\\" cy=\\\"12\\\" cx=\\\"24\\\" data-follow-fill=\\\"currentColor\\\"/><circle fill=\\\"currentColor\\\" r=\\\"3\\\" cy=\\\"24\\\" cx=\\\"24\\\" data-follow-fill=\\\"currentColor\\\"/><circle fill=\\\"currentColor\\\" r=\\\"3\\\" cy=\\\"35\\\" cx=\\\"24\\\" data-follow-fill=\\\"currentColor\\\"/></g>\"},\"1368846\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><ellipse stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" ry=\\\"6\\\" rx=\\\"20\\\" cy=\\\"11\\\" cx=\\\"24\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M10.77 15.5C6.62 16.6 4 18.208 4 20c0 3.314 8.954 6 20 6s20-2.686 20-6c0-1.792-2.619-3.4-6.77-4.5-3.526.933-8.158 1.5-13.23 1.5-5.072 0-9.704-.567-13.23-1.5Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M10.77 24.5C6.62 25.6 4 27.208 4 29c0 3.314 8.954 6 20 6s20-2.686 20-6c0-1.792-2.619-3.4-6.77-4.5-3.526.933-8.158 1.5-13.23 1.5-5.072 0-9.704-.567-13.23-1.5Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M10.77 33.5C6.62 34.6 4 36.208 4 38c0 3.314 8.954 6 20 6s20-2.686 20-6c0-1.792-2.619-3.4-6.77-4.5-3.526.934-8.158 1.5-13.23 1.5-5.072 0-9.704-.566-13.23-1.5Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1368847\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M9 10v34h30V10H9Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M20 20v13M28 20v13M4 10h40\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"m16 10 3.289-6h9.488L32 10H16Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1368858\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M16 13 4 25.432 16 37M32 13l12 12.432L32 37\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"m28 4-7 40\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1368859\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M39.3 6H8.7A2.7 2.7 0 0 0 6 8.7v30.6A2.7 2.7 0 0 0 8.7 42h30.6a2.7 2.7 0 0 0 2.7-2.7V8.7A2.7 2.7 0 0 0 39.3 6Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M32 6v18H15V6h17Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M26 13v4M10.997 6H36\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1368864\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M13 12.432v-4.62A2.813 2.813 0 0 1 15.813 5h24.374A2.813 2.813 0 0 1 43 7.813v24.375A2.813 2.813 0 0 1 40.187 35h-4.67\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M32.188 13H7.811A2.813 2.813 0 0 0 5 15.813v24.374A2.813 2.813 0 0 0 7.813 43h24.375A2.813 2.813 0 0 0 35 40.187V15.814A2.813 2.813 0 0 0 32.187 13Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1368865\":{\"viewBox\":\"0 0 24 24\",\"fill\":\"currentColor\",\"content\":\"<g><g fill=\\\"none\\\" fill-rule=\\\"evenodd\\\"><path d=\\\"m12.593 23.258-.011.002-.071.035-.02.004-.014-.004-.071-.035q-.016-.005-.024.005l-.004.01-.017.428.005.02.01.013.104.074.015.004.012-.004.104-.074.012-.016.004-.017-.017-.427q-.004-.016-.017-.018m.265-.113-.013.002-.185.093-.01.01-.003.011.018.43.005.012.008.007.201.093q.019.005.029-.008l.004-.014-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014-.034.614q.001.018.017.024l.015-.002.201-.093.01-.008.004-.011.017-.43-.003-.012-.01-.01z\\\"/><path data-follow-fill=\\\"currentColor\\\" fill=\\\"currentColor\\\" d=\\\"M10.5 4a1.472 1.472 0 0 0-1.317 2.13l.163.325A1.067 1.067 0 0 1 8.39 8H5a1 1 0 0 0-1 1v1.194c1.82-.109 3.5 1.331 3.5 3.306S5.82 16.915 4 16.806V19a1 1 0 0 0 1 1h2.194c-.109-1.82 1.331-3.5 3.306-3.5s3.415 1.68 3.306 3.5H15a1 1 0 0 0 1-1v-3.39c0-.794.835-1.31 1.545-.956l.324.163a1.472 1.472 0 1 0 0-2.634l-.324.163A1.067 1.067 0 0 1 16 11.39V9a1 1 0 0 0-1-1h-2.39c-.794 0-1.31-.835-.956-1.545l.163-.325A1.472 1.472 0 0 0 10.5 4M7.064 6c-.316-2.017 1.23-4 3.436-4s3.752 1.983 3.436 4H15a3 3 0 0 1 3 3v1.064c2.017-.316 4 1.23 4 3.436s-1.983 3.752-4 3.436V19a3 3 0 0 1-3 3h-2.407a1.06 1.06 0 0 1-.976-1.48l.085-.197a1.308 1.308 0 1 0-2.404 0l.085.198c.3.7-.214 1.479-.976 1.479H5a3 3 0 0 1-3-3v-3.407c0-.762.779-1.276 1.48-.976l.197.085a1.308 1.308 0 1 0 0-2.404l-.198.085c-.7.3-1.479-.214-1.479-.976V9a3 3 0 0 1 3-3z\\\"/></g></g>\"},\"1368866\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M4.592 19.467A2 2 0 0 1 6.537 17h34.926a2 2 0 0 1 1.945 2.467l-5.04 21A2 2 0 0 1 36.423 42H11.577a2 2 0 0 1-1.945-1.533l-5.04-21Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M11 7h8v10h-8zM19 17l6.5-9L38 17\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M15 25h8\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1368914\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M5.325 43.5h8.485l31.113-31.113-8.486-8.485L5.325 35.015V43.5Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"m27.952 12.387 8.485 8.485\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1396721\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><rect stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" rx=\\\"3\\\" height=\\\"36\\\" width=\\\"36\\\" y=\\\"6\\\" x=\\\"6\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M13 13h8v8h-8zM27 13h8v8h-8zM13 27h8v8h-8zM27 27h8v8h-8z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1396722\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><rect stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" rx=\\\"3\\\" height=\\\"36\\\" width=\\\"36\\\" y=\\\"6\\\" x=\\\"6\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M13 13h8v8h-8zM13 27h8v8h-8z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M27 28h8M27 35h8M27 13h8M27 20h8\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1396723\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M39 6H9a3 3 0 0 0-3 3v30a3 3 0 0 0 3 3h30a3 3 0 0 0 3-3V9a3 3 0 0 0-3-3Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M18 23a5 5 0 1 0 0-10 5 5 0 0 0 0 10ZM27.79 26.22a2 2 0 0 1 3.243.053l8.775 12.583c.924 1.326-.025 3.144-1.64 3.144H16l11.79-15.78Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1397326\":{\"viewBox\":\"0 0 1024 1024\",\"content\":\"<g><path fill=\\\"#37AFFA\\\" d=\\\"m490.2 221.7-47.1-49.9c-13.8-14.6-32.9-22.9-52.9-22.9H186.1c-40.4 0-73.2 33-73.2 73.8v548.1c0 40.8 32.8 73.8 73.2 73.8h653.7c40.4 0 73.2-33 73.2-73.8V318.4c0-40.8-32.8-73.8-73.2-73.8H543.2c-20 0-39.2-8.3-53-22.9\\\"/><path fill=\\\"#37AFFA\\\" d=\\\"M194 149h-7.9c-40.4 0-73.2 33-73.2 73.8v14.8c23-33.1 50.3-62.9 81.1-88.6m718.9 169.4c0-37.1-27.2-67.9-62.6-73 29 43.8 50.5 92.9 62.6 145.7zm0 452.4V591.2c-23.5 102.5-82.5 191.5-163.2 253.4h90.1c40.4 0 73.1-33 73.1-73.8m-800-26.1v26.2c0 40.8 32.8 73.8 73.2 73.8h22.2c-36.7-28.2-68.9-62-95.4-100\\\"/><path fill=\\\"#41B6FB\\\" d=\\\"M249.1 149h-55c-30.8 25.7-58.1 55.5-81.1 88.6v67.6c30.3-63.7 77.6-117.7 136.1-156.2M112.9 635.2v109.5c26.4 38.1 58.6 71.8 95.3 100h165.2c-115.2-26.6-210.7-105.1-260.5-209.5m800-44V391c-12.1-52.8-33.6-101.9-62.6-145.7-3.4-.5-7-.8-10.5-.8h-68.7c46 63.4 73.2 141.3 73.2 225.6 0 182.5-127.2 335.2-297.7 374.5h203.2c80.7-61.9 139.6-150.9 163.1-253.4\\\"/><path fill=\\\"#4CBCFC\\\" d=\\\"M844.3 470.2c0-84.3-27.1-162.3-73.2-225.6h-79.9c45.7 55.7 73.1 127 73.1 204.7 0 178.5-144.7 323.2-323.2 323.2S117.8 627.8 117.8 449.2c0-136.2 84.2-252.6 203.4-300.3H249c-58.5 38.5-105.8 92.5-136.1 156.2v330.1c49.7 104.4 145.2 182.9 260.6 209.4h173c170.6-39.2 297.8-192 297.8-374.4\\\"/><path fill=\\\"#56C3FD\\\" d=\\\"M117.8 449.2c0 178.5 144.7 323.2 323.2 323.2s323.2-144.7 323.2-323.2c0-77.7-27.4-148.9-73.1-204.7H609c46.5 47.3 75.2 112.2 75.2 183.7 0 144.8-117.4 262.2-262.2 262.2S159.9 573.1 159.9 428.3s117.4-262.2 262.2-262.2c5.2 0 10.3.2 15.4.5-13.2-11.3-29.9-17.6-47.4-17.6h-68.9C202 196.6 117.8 313.1 117.8 449.2\\\"/><path fill=\\\"#60C9FD\\\" d=\\\"M159.9 428.3c0 144.8 117.4 262.2 262.2 262.2s262.2-117.4 262.2-262.2c0-71.6-28.7-136.4-75.2-183.7h-66c-20 0-39.1-8.3-52.9-22.9l-47.1-49.9c-1.8-1.9-3.6-3.6-5.6-5.3-5.1-.3-10.2-.5-15.4-.5-144.8.1-262.2 117.5-262.2 262.3m444.5-20.9c0 111.1-90.1 201.2-201.2 201.2S202 518.5 202 407.4s90-201.2 201.2-201.2 201.2 90.1 201.2 201.2\\\"/><path fill=\\\"#6BCFFE\\\" d=\\\"M202 407.4c0 111.1 90.1 201.2 201.2 201.2s201.2-90.1 201.2-201.2-90.1-201.2-201.2-201.2S202 296.3 202 407.4m322.4-21c0 77.4-62.8 140.2-140.2 140.2S244 463.9 244 386.4s62.8-140.2 140.2-140.2S524.4 309 524.4 386.4\\\"/><path fill=\\\"#75D6FF\\\" d=\\\"M244 386.4a140.2 140.2 0 1 0 280.4 0 140.2 140.2 0 1 0-280.4 0\\\"/><path fill=\\\"#37AFFA\\\" d=\\\"M390.2 167c15 0 29.5 6.3 39.9 17.2l47.1 49.9c17.1 18.1 41.2 28.5 66 28.5h296.6c30.4 0 55.2 25 55.2 55.8v452.5c0 30.8-24.7 55.8-55.2 55.8H186.1c-30.4 0-55.2-25-55.2-55.8V222.8c0-30.8 24.7-55.8 55.2-55.8zm0-18H186.1c-40.4 0-73.2 33-73.2 73.8v548.1c0 40.8 32.8 73.8 73.2 73.8h653.7c40.4 0 73.2-33 73.2-73.8V318.4c0-40.8-32.8-73.8-73.2-73.8H543.2c-20 0-39.1-8.3-52.9-22.9l-47.1-49.9c-13.9-14.6-33-22.8-53-22.8\\\"/><path fill=\\\"#B9E8FF\\\" d=\\\"M130.9 353.5h556.5c19.2 0 34.8 15.6 34.8 34.8v139.1c0 19.2-15.6 34.8-34.8 34.8H130.9z\\\"/><path fill=\\\"#FFF\\\" d=\\\"M379.3 226.2H255.2c-9.9 0-18-8.1-18-18s8.1-18 18-18h124.1c9.9 0 18 8.1 18 18s-8.1 18-18 18m-184.1 0h-10.5c-9.9 0-18-8.1-18-18s8.1-18 18-18h10.5c9.9 0 18 8.1 18 18s-8 18-18 18\\\"/></g>\"},\"1397327\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M24 44c8.235 0 15-6.526 15-14.902 0-2.056-.105-4.26-1.245-7.686-1.14-3.426-1.369-3.868-2.574-5.984-.515 4.317-3.27 6.117-3.97 6.655 0-.56-1.666-6.747-4.193-10.45C24.537 8 21.163 5.617 19.185 4c0 3.07-.863 7.634-2.1 9.96-1.236 2.325-1.468 2.41-3.013 4.14-1.544 1.73-2.253 2.265-3.545 4.365C9.236 24.565 9 27.362 9 29.418 9 37.794 15.765 44 24 44Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1467306\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M4 9v32l5-20h30.5v-6a2 2 0 0 0-2-2H24l-5-6H6a2 2 0 0 0-2 2Z\\\" data-follow-stroke=\\\"currentColor\\\"/><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"m40 41 4-20H8.812L4 41h36Z\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"},\"1467309\":{\"viewBox\":\"0 0 48 48\",\"fill\":\"none\",\"content\":\"<g><path stroke-linejoin=\\\"round\\\" stroke-linecap=\\\"round\\\" stroke-width=\\\"4\\\" stroke=\\\"currentColor\\\" d=\\\"M42 19H6M30 7l12 12M6.799 29h36M6.799 29l12 12\\\" data-follow-stroke=\\\"currentColor\\\"/></g>\"}}");for(var _k in obj){window.__iconpark__[_k] = obj[_k]};var nm={"upload-one":1368803,"cloud-storage":1368816,"application-effect":1368817,"setting-one":1368818,"plus":1368837,"more-one":1368843,"data-all":1368846,"delete":1368847,"code":1368858,"save-one":1368859,"copy":1368864,"plugin":1368865,"receive":1368866,"write":1368914,"view-grid-card":1396721,"view-grid-list":1396722,"picture":1396723,"file":1397326,"fire":1397327,"folder-open":1467306,"switch":1467309};for(var _i in nm){window.__iconpark__[_i] = obj[nm[_i]]}})();"object"!=typeof globalThis&&(Object.prototype.__defineGetter__("__magic__",function(){return this}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__);(()=>{"use strict";var t={816:(t,e,i)=>{var s,r,o,n;i.d(e,{Vm:()=>z,dy:()=>P,Jb:()=>x,Ld:()=>$,sY:()=>T,YP:()=>A});const l=globalThis.trustedTypes,a=l?l.createPolicy("lit-html",{createHTML:t=>t}):void 0,h=`lit$${(Math.random()+"").slice(9)}$`,c="?"+h,d=`<${c}>`,u=document,p=(t="")=>u.createComment(t),v=t=>null===t||"object"!=typeof t&&"function"!=typeof t,f=Array.isArray,y=t=>{var e;return f(t)||"function"==typeof(null===(e=t)||void 0===e?void 0:e[Symbol.iterator])},m=/<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g,g=/-->/g,b=/>/g,S=/>|[ 	\n\r](?:([^\s"'>=/]+)([ 	\n\r]*=[ 	\n\r]*(?:[^ 	\n\r"'`<>=]|("|')|))|$)/g,w=/'/g,k=/"/g,E=/^(?:script|style|textarea)$/i,C=t=>(e,...i)=>({_$litType$:t,strings:e,values:i}),P=C(1),A=C(2),x=Symbol.for("lit-noChange"),$=Symbol.for("lit-nothing"),O=new WeakMap,T=(t,e,i)=>{var s,r;const o=null!==(s=null==i?void 0:i.renderBefore)&&void 0!==s?s:e;let n=o._$litPart$;if(void 0===n){const t=null!==(r=null==i?void 0:i.renderBefore)&&void 0!==r?r:null;o._$litPart$=n=new H(e.insertBefore(p(),t),t,void 0,i)}return n.I(t),n},R=u.createTreeWalker(u,129,null,!1),_=(t,e)=>{const i=t.length-1,s=[];let r,o=2===e?"<svg>":"",n=m;for(let e=0;e<i;e++){const i=t[e];let l,a,c=-1,u=0;for(;u<i.length&&(n.lastIndex=u,a=n.exec(i),null!==a);)u=n.lastIndex,n===m?"!--"===a[1]?n=g:void 0!==a[1]?n=b:void 0!==a[2]?(E.test(a[2])&&(r=RegExp("</"+a[2],"g")),n=S):void 0!==a[3]&&(n=S):n===S?">"===a[0]?(n=null!=r?r:m,c=-1):void 0===a[1]?c=-2:(c=n.lastIndex-a[2].length,l=a[1],n=void 0===a[3]?S:'"'===a[3]?k:w):n===k||n===w?n=S:n===g||n===b?n=m:(n=S,r=void 0);const p=n===S&&t[e+1].startsWith("/>")?" ":"";o+=n===m?i+d:c>=0?(s.push(l),i.slice(0,c)+"$lit$"+i.slice(c)+h+p):i+h+(-2===c?(s.push(void 0),e):p)}const l=o+(t[i]||"<?>")+(2===e?"</svg>":"");return[void 0!==a?a.createHTML(l):l,s]};class N{constructor({strings:t,_$litType$:e},i){let s;this.parts=[];let r=0,o=0;const n=t.length-1,a=this.parts,[d,u]=_(t,e);if(this.el=N.createElement(d,i),R.currentNode=this.el.content,2===e){const t=this.el.content,e=t.firstChild;e.remove(),t.append(...e.childNodes)}for(;null!==(s=R.nextNode())&&a.length<n;){if(1===s.nodeType){if(s.hasAttributes()){const t=[];for(const e of s.getAttributeNames())if(e.endsWith("$lit$")||e.startsWith(h)){const i=u[o++];if(t.push(e),void 0!==i){const t=s.getAttribute(i.toLowerCase()+"$lit$").split(h),e=/([.?@])?(.*)/.exec(i);a.push({type:1,index:r,name:e[2],strings:t,ctor:"."===e[1]?I:"?"===e[1]?j:"@"===e[1]?B:M})}else a.push({type:6,index:r})}for(const e of t)s.removeAttribute(e)}if(E.test(s.tagName)){const t=s.textContent.split(h),e=t.length-1;if(e>0){s.textContent=l?l.emptyScript:"";for(let i=0;i<e;i++)s.append(t[i],p()),R.nextNode(),a.push({type:2,index:++r});s.append(t[e],p())}}}else if(8===s.nodeType)if(s.data===c)a.push({type:2,index:r});else{let t=-1;for(;-1!==(t=s.data.indexOf(h,t+1));)a.push({type:7,index:r}),t+=h.length-1}r++}}static createElement(t,e){const i=u.createElement("template");return i.innerHTML=t,i}}function U(t,e,i=t,s){var r,o,n,l;if(e===x)return e;let a=void 0!==s?null===(r=i.Σi)||void 0===r?void 0:r[s]:i.Σo;const h=v(e)?void 0:e._$litDirective$;return(null==a?void 0:a.constructor)!==h&&(null===(o=null==a?void 0:a.O)||void 0===o||o.call(a,!1),void 0===h?a=void 0:(a=new h(t),a.T(t,i,s)),void 0!==s?(null!==(n=(l=i).Σi)&&void 0!==n?n:l.Σi=[])[s]=a:i.Σo=a),void 0!==a&&(e=U(t,a.S(t,e.values),a,s)),e}class L{constructor(t,e){this.l=[],this.N=void 0,this.D=t,this.M=e}u(t){var e;const{el:{content:i},parts:s}=this.D,r=(null!==(e=null==t?void 0:t.creationScope)&&void 0!==e?e:u).importNode(i,!0);R.currentNode=r;let o=R.nextNode(),n=0,l=0,a=s[0];for(;void 0!==a;){if(n===a.index){let e;2===a.type?e=new H(o,o.nextSibling,this,t):1===a.type?e=new a.ctor(o,a.name,a.strings,this,t):6===a.type&&(e=new V(o,this,t)),this.l.push(e),a=s[++l]}n!==(null==a?void 0:a.index)&&(o=R.nextNode(),n++)}return r}v(t){let e=0;for(const i of this.l)void 0!==i&&(void 0!==i.strings?(i.I(t,i,e),e+=i.strings.length-2):i.I(t[e])),e++}}class H{constructor(t,e,i,s){this.type=2,this.N=void 0,this.A=t,this.B=e,this.M=i,this.options=s}setConnected(t){var e;null===(e=this.P)||void 0===e||e.call(this,t)}get parentNode(){return this.A.parentNode}get startNode(){return this.A}get endNode(){return this.B}I(t,e=this){t=U(this,t,e),v(t)?t===$||null==t||""===t?(this.H!==$&&this.R(),this.H=$):t!==this.H&&t!==x&&this.m(t):void 0!==t._$litType$?this._(t):void 0!==t.nodeType?this.$(t):y(t)?this.g(t):this.m(t)}k(t,e=this.B){return this.A.parentNode.insertBefore(t,e)}$(t){this.H!==t&&(this.R(),this.H=this.k(t))}m(t){const e=this.A.nextSibling;null!==e&&3===e.nodeType&&(null===this.B?null===e.nextSibling:e===this.B.previousSibling)?e.data=t:this.$(u.createTextNode(t)),this.H=t}_(t){var e;const{values:i,_$litType$:s}=t,r="number"==typeof s?this.C(t):(void 0===s.el&&(s.el=N.createElement(s.h,this.options)),s);if((null===(e=this.H)||void 0===e?void 0:e.D)===r)this.H.v(i);else{const t=new L(r,this),e=t.u(this.options);t.v(i),this.$(e),this.H=t}}C(t){let e=O.get(t.strings);return void 0===e&&O.set(t.strings,e=new N(t)),e}g(t){f(this.H)||(this.H=[],this.R());const e=this.H;let i,s=0;for(const r of t)s===e.length?e.push(i=new H(this.k(p()),this.k(p()),this,this.options)):i=e[s],i.I(r),s++;s<e.length&&(this.R(i&&i.B.nextSibling,s),e.length=s)}R(t=this.A.nextSibling,e){var i;for(null===(i=this.P)||void 0===i||i.call(this,!1,!0,e);t&&t!==this.B;){const e=t.nextSibling;t.remove(),t=e}}}class M{constructor(t,e,i,s,r){this.type=1,this.H=$,this.N=void 0,this.V=void 0,this.element=t,this.name=e,this.M=s,this.options=r,i.length>2||""!==i[0]||""!==i[1]?(this.H=Array(i.length-1).fill($),this.strings=i):this.H=$}get tagName(){return this.element.tagName}I(t,e=this,i,s){const r=this.strings;let o=!1;if(void 0===r)t=U(this,t,e,0),o=!v(t)||t!==this.H&&t!==x,o&&(this.H=t);else{const s=t;let n,l;for(t=r[0],n=0;n<r.length-1;n++)l=U(this,s[i+n],e,n),l===x&&(l=this.H[n]),o||(o=!v(l)||l!==this.H[n]),l===$?t=$:t!==$&&(t+=(null!=l?l:"")+r[n+1]),this.H[n]=l}o&&!s&&this.W(t)}W(t){t===$?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,null!=t?t:"")}}class I extends M{constructor(){super(...arguments),this.type=3}W(t){this.element[this.name]=t===$?void 0:t}}class j extends M{constructor(){super(...arguments),this.type=4}W(t){t&&t!==$?this.element.setAttribute(this.name,""):this.element.removeAttribute(this.name)}}class B extends M{constructor(){super(...arguments),this.type=5}I(t,e=this){var i;if((t=null!==(i=U(this,t,e,0))&&void 0!==i?i:$)===x)return;const s=this.H,r=t===$&&s!==$||t.capture!==s.capture||t.once!==s.once||t.passive!==s.passive,o=t!==$&&(s===$||r);r&&this.element.removeEventListener(this.name,this,s),o&&this.element.addEventListener(this.name,this,t),this.H=t}handleEvent(t){var e,i;"function"==typeof this.H?this.H.call(null!==(i=null===(e=this.options)||void 0===e?void 0:e.host)&&void 0!==i?i:this.element,t):this.H.handleEvent(t)}}class V{constructor(t,e,i){this.element=t,this.type=6,this.N=void 0,this.V=void 0,this.M=e,this.options=i}I(t){U(this,t)}}const z={Z:"$lit$",U:h,Y:c,q:1,X:_,tt:L,it:y,st:U,et:H,ot:M,nt:j,rt:B,lt:I,ht:V};null===(r=(s=globalThis).litHtmlPlatformSupport)||void 0===r||r.call(s,N,H),(null!==(o=(n=globalThis).litHtmlVersions)&&void 0!==o?o:n.litHtmlVersions=[]).push("2.0.0-rc.2")},26:(t,e,i)=>{i.r(e),i.d(e,{customElement:()=>s,eventOptions:()=>a,property:()=>o,query:()=>h,queryAll:()=>c,queryAssignedNodes:()=>v,queryAsync:()=>d,state:()=>n});const s=t=>e=>"function"==typeof e?((t,e)=>(window.customElements.define(t,e),e))(t,e):((t,e)=>{const{kind:i,elements:s}=e;return{kind:i,elements:s,finisher(e){window.customElements.define(t,e)}}})(t,e),r=(t,e)=>"method"===e.kind&&e.descriptor&&!("value"in e.descriptor)?{...e,finisher(i){i.createProperty(e.key,t)}}:{kind:"field",key:Symbol(),placement:"own",descriptor:{},originalKey:e.key,initializer(){"function"==typeof e.initializer&&(this[e.key]=e.initializer.call(this))},finisher(i){i.createProperty(e.key,t)}};function o(t){return(e,i)=>void 0!==i?((t,e,i)=>{e.constructor.createProperty(i,t)})(t,e,i):r(t,e)}function n(t){return o({...t,state:!0,attribute:!1})}const l=({finisher:t,descriptor:e})=>(i,s)=>{var r;if(void 0===s){const s=null!==(r=i.originalKey)&&void 0!==r?r:i.key,o=null!=e?{kind:"method",placement:"prototype",key:s,descriptor:e(i.key)}:{...i,key:s};return null!=t&&(o.finisher=function(e){t(e,s)}),o}{const r=i.constructor;void 0!==e&&Object.defineProperty(i,s,e(s)),null==t||t(r,s)}};function a(t){return l({finisher:(e,i)=>{Object.assign(e.prototype[i],t)}})}function h(t,e){return l({descriptor:i=>{const s={get(){var e;return null===(e=this.renderRoot)||void 0===e?void 0:e.querySelector(t)},enumerable:!0,configurable:!0};if(e){const e="symbol"==typeof i?Symbol():"__"+i;s.get=function(){var i;return void 0===this[e]&&(this[e]=null===(i=this.renderRoot)||void 0===i?void 0:i.querySelector(t)),this[e]}}return s}})}function c(t){return l({descriptor:e=>({get(){var e;return null===(e=this.renderRoot)||void 0===e?void 0:e.querySelectorAll(t)},enumerable:!0,configurable:!0})})}function d(t){return l({descriptor:e=>({async get(){var e;return await this.updateComplete,null===(e=this.renderRoot)||void 0===e?void 0:e.querySelector(t)},enumerable:!0,configurable:!0})})}const u=Element.prototype,p=u.msMatchesSelector||u.webkitMatchesSelector;function v(t="",e=!1,i=""){return l({descriptor:s=>({get(){var s,r;const o="slot"+(t?`[name=${t}]`:":not([name])");let n=null===(r=null===(s=this.renderRoot)||void 0===s?void 0:s.querySelector(o))||void 0===r?void 0:r.assignedNodes({flatten:e});return n&&i&&(n=n.filter((t=>t.nodeType===Node.ELEMENT_NODE&&(t.matches?t.matches(i):p.call(t,i))))),n},enumerable:!0,configurable:!0})})}},23:(t,e,i)=>{i.r(e),i.d(e,{unsafeSVG:()=>l});const s=t=>(...e)=>({_$litDirective$:t,values:e});var r=i(816);class o extends class{constructor(t){}T(t,e,i){this.Σdt=t,this.M=e,this.Σct=i}S(t,e){return this.update(t,e)}update(t,e){return this.render(...e)}}{constructor(t){if(super(t),this.vt=r.Ld,2!==t.type)throw Error(this.constructor.directiveName+"() can only be used in child bindings")}render(t){if(t===r.Ld)return this.Vt=void 0,this.vt=t;if(t===r.Jb)return t;if("string"!=typeof t)throw Error(this.constructor.directiveName+"() called with a non-string value");if(t===this.vt)return this.Vt;this.vt=t;const e=[t];return e.raw=e,this.Vt={_$litType$:this.constructor.resultType,strings:e,values:[]}}}o.directiveName="unsafeHTML",o.resultType=1,s(o);class n extends o{}n.directiveName="unsafeSVG",n.resultType=2;const l=s(n)},249:(t,e,i)=>{i.r(e),i.d(e,{CSSResult:()=>n,LitElement:()=>x,ReactiveElement:()=>b,UpdatingElement:()=>A,_Σ:()=>s.Vm,_Φ:()=>$,adoptStyles:()=>c,css:()=>h,defaultConverter:()=>y,getCompatibleStyle:()=>d,html:()=>s.dy,noChange:()=>s.Jb,notEqual:()=>m,nothing:()=>s.Ld,render:()=>s.sY,supportsAdoptingStyleSheets:()=>r,svg:()=>s.YP,unsafeCSS:()=>l});var s=i(816);const r=window.ShadowRoot&&(void 0===window.ShadyCSS||window.ShadyCSS.nativeShadow)&&"adoptedStyleSheets"in Document.prototype&&"replace"in CSSStyleSheet.prototype,o=Symbol();class n{constructor(t,e){if(e!==o)throw Error("CSSResult is not constructable. Use `unsafeCSS` or `css` instead.");this.cssText=t}get styleSheet(){return r&&void 0===this.t&&(this.t=new CSSStyleSheet,this.t.replaceSync(this.cssText)),this.t}toString(){return this.cssText}}const l=t=>new n(t+"",o),a=new Map,h=(t,...e)=>{const i=e.reduce(((e,i,s)=>e+(t=>{if(t instanceof n)return t.cssText;if("number"==typeof t)return t;throw Error(`Value passed to 'css' function must be a 'css' function result: ${t}. Use 'unsafeCSS' to pass non-literal values, but\n            take care to ensure page security.`)})(i)+t[s+1]),t[0]);let s=a.get(i);return void 0===s&&a.set(i,s=new n(i,o)),s},c=(t,e)=>{r?t.adoptedStyleSheets=e.map((t=>t instanceof CSSStyleSheet?t:t.styleSheet)):e.forEach((e=>{const i=document.createElement("style");i.textContent=e.cssText,t.appendChild(i)}))},d=r?t=>t:t=>t instanceof CSSStyleSheet?(t=>{let e="";for(const i of t.cssRules)e+=i.cssText;return l(e)})(t):t;var u,p,v,f;const y={toAttribute(t,e){switch(e){case Boolean:t=t?"":null;break;case Object:case Array:t=null==t?t:JSON.stringify(t)}return t},fromAttribute(t,e){let i=t;switch(e){case Boolean:i=null!==t;break;case Number:i=null===t?null:Number(t);break;case Object:case Array:try{i=JSON.parse(t)}catch(t){i=null}}return i}},m=(t,e)=>e!==t&&(e==e||t==t),g={attribute:!0,type:String,converter:y,reflect:!1,hasChanged:m};class b extends HTMLElement{constructor(){super(),this.Πi=new Map,this.Πo=void 0,this.Πl=void 0,this.isUpdatePending=!1,this.hasUpdated=!1,this.Πh=null,this.u()}static addInitializer(t){var e;null!==(e=this.v)&&void 0!==e||(this.v=[]),this.v.push(t)}static get observedAttributes(){this.finalize();const t=[];return this.elementProperties.forEach(((e,i)=>{const s=this.Πp(i,e);void 0!==s&&(this.Πm.set(s,i),t.push(s))})),t}static createProperty(t,e=g){if(e.state&&(e.attribute=!1),this.finalize(),this.elementProperties.set(t,e),!e.noAccessor&&!this.prototype.hasOwnProperty(t)){const i="symbol"==typeof t?Symbol():"__"+t,s=this.getPropertyDescriptor(t,i,e);void 0!==s&&Object.defineProperty(this.prototype,t,s)}}static getPropertyDescriptor(t,e,i){return{get(){return this[e]},set(s){const r=this[t];this[e]=s,this.requestUpdate(t,r,i)},configurable:!0,enumerable:!0}}static getPropertyOptions(t){return this.elementProperties.get(t)||g}static finalize(){if(this.hasOwnProperty("finalized"))return!1;this.finalized=!0;const t=Object.getPrototypeOf(this);if(t.finalize(),this.elementProperties=new Map(t.elementProperties),this.Πm=new Map,this.hasOwnProperty("properties")){const t=this.properties,e=[...Object.getOwnPropertyNames(t),...Object.getOwnPropertySymbols(t)];for(const i of e)this.createProperty(i,t[i])}return this.elementStyles=this.finalizeStyles(this.styles),!0}static finalizeStyles(t){const e=[];if(Array.isArray(t)){const i=new Set(t.flat(1/0).reverse());for(const t of i)e.unshift(d(t))}else void 0!==t&&e.push(d(t));return e}static Πp(t,e){const i=e.attribute;return!1===i?void 0:"string"==typeof i?i:"string"==typeof t?t.toLowerCase():void 0}u(){var t;this.Πg=new Promise((t=>this.enableUpdating=t)),this.L=new Map,this.Π_(),this.requestUpdate(),null===(t=this.constructor.v)||void 0===t||t.forEach((t=>t(this)))}addController(t){var e,i;(null!==(e=this.ΠU)&&void 0!==e?e:this.ΠU=[]).push(t),void 0!==this.renderRoot&&this.isConnected&&(null===(i=t.hostConnected)||void 0===i||i.call(t))}removeController(t){var e;null===(e=this.ΠU)||void 0===e||e.splice(this.ΠU.indexOf(t)>>>0,1)}Π_(){this.constructor.elementProperties.forEach(((t,e)=>{this.hasOwnProperty(e)&&(this.Πi.set(e,this[e]),delete this[e])}))}createRenderRoot(){var t;const e=null!==(t=this.shadowRoot)&&void 0!==t?t:this.attachShadow(this.constructor.shadowRootOptions);return c(e,this.constructor.elementStyles),e}connectedCallback(){var t;void 0===this.renderRoot&&(this.renderRoot=this.createRenderRoot()),this.enableUpdating(!0),null===(t=this.ΠU)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostConnected)||void 0===e?void 0:e.call(t)})),this.Πl&&(this.Πl(),this.Πo=this.Πl=void 0)}enableUpdating(t){}disconnectedCallback(){var t;null===(t=this.ΠU)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostDisconnected)||void 0===e?void 0:e.call(t)})),this.Πo=new Promise((t=>this.Πl=t))}attributeChangedCallback(t,e,i){this.K(t,i)}Πj(t,e,i=g){var s,r;const o=this.constructor.Πp(t,i);if(void 0!==o&&!0===i.reflect){const n=(null!==(r=null===(s=i.converter)||void 0===s?void 0:s.toAttribute)&&void 0!==r?r:y.toAttribute)(e,i.type);this.Πh=t,null==n?this.removeAttribute(o):this.setAttribute(o,n),this.Πh=null}}K(t,e){var i,s,r;const o=this.constructor,n=o.Πm.get(t);if(void 0!==n&&this.Πh!==n){const t=o.getPropertyOptions(n),l=t.converter,a=null!==(r=null!==(s=null===(i=l)||void 0===i?void 0:i.fromAttribute)&&void 0!==s?s:"function"==typeof l?l:null)&&void 0!==r?r:y.fromAttribute;this.Πh=n,this[n]=a(e,t.type),this.Πh=null}}requestUpdate(t,e,i){let s=!0;void 0!==t&&(((i=i||this.constructor.getPropertyOptions(t)).hasChanged||m)(this[t],e)?(this.L.has(t)||this.L.set(t,e),!0===i.reflect&&this.Πh!==t&&(void 0===this.Πk&&(this.Πk=new Map),this.Πk.set(t,i))):s=!1),!this.isUpdatePending&&s&&(this.Πg=this.Πq())}async Πq(){this.isUpdatePending=!0;try{for(await this.Πg;this.Πo;)await this.Πo}catch(t){Promise.reject(t)}const t=this.performUpdate();return null!=t&&await t,!this.isUpdatePending}performUpdate(){var t;if(!this.isUpdatePending)return;this.hasUpdated,this.Πi&&(this.Πi.forEach(((t,e)=>this[e]=t)),this.Πi=void 0);let e=!1;const i=this.L;try{e=this.shouldUpdate(i),e?(this.willUpdate(i),null===(t=this.ΠU)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostUpdate)||void 0===e?void 0:e.call(t)})),this.update(i)):this.Π$()}catch(t){throw e=!1,this.Π$(),t}e&&this.E(i)}willUpdate(t){}E(t){var e;null===(e=this.ΠU)||void 0===e||e.forEach((t=>{var e;return null===(e=t.hostUpdated)||void 0===e?void 0:e.call(t)})),this.hasUpdated||(this.hasUpdated=!0,this.firstUpdated(t)),this.updated(t)}Π$(){this.L=new Map,this.isUpdatePending=!1}get updateComplete(){return this.getUpdateComplete()}getUpdateComplete(){return this.Πg}shouldUpdate(t){return!0}update(t){void 0!==this.Πk&&(this.Πk.forEach(((t,e)=>this.Πj(e,this[e],t))),this.Πk=void 0),this.Π$()}updated(t){}firstUpdated(t){}}var S,w,k,E,C,P;b.finalized=!0,b.shadowRootOptions={mode:"open"},null===(p=(u=globalThis).reactiveElementPlatformSupport)||void 0===p||p.call(u,{ReactiveElement:b}),(null!==(v=(f=globalThis).reactiveElementVersions)&&void 0!==v?v:f.reactiveElementVersions=[]).push("1.0.0-rc.1");const A=b;(null!==(S=(P=globalThis).litElementVersions)&&void 0!==S?S:P.litElementVersions=[]).push("3.0.0-rc.1");class x extends b{constructor(){super(...arguments),this.renderOptions={host:this},this.Φt=void 0}createRenderRoot(){var t,e;const i=super.createRenderRoot();return null!==(t=(e=this.renderOptions).renderBefore)&&void 0!==t||(e.renderBefore=i.firstChild),i}update(t){const e=this.render();super.update(t),this.Φt=(0,s.sY)(e,this.renderRoot,this.renderOptions)}connectedCallback(){var t;super.connectedCallback(),null===(t=this.Φt)||void 0===t||t.setConnected(!0)}disconnectedCallback(){var t;super.disconnectedCallback(),null===(t=this.Φt)||void 0===t||t.setConnected(!1)}render(){return s.Jb}}x.finalized=!0,x._$litElement$=!0,null===(k=(w=globalThis).litElementHydrateSupport)||void 0===k||k.call(w,{LitElement:x}),null===(C=(E=globalThis).litElementPlatformSupport)||void 0===C||C.call(E,{LitElement:x});const $={K:(t,e,i)=>{t.K(e,i)},L:t=>t.L}},409:function(t,e,i){var s=this&&this.__decorate||function(t,e,i,s){var r,o=arguments.length,n=o<3?e:null===s?s=Object.getOwnPropertyDescriptor(e,i):s;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)n=Reflect.decorate(t,e,i,s);else for(var l=t.length-1;l>=0;l--)(r=t[l])&&(n=(o<3?r(n):o>3?r(e,i,n):r(e,i))||n);return o>3&&n&&Object.defineProperty(e,i,n),n};Object.defineProperty(e,"__esModule",{value:!0}),e.IconparkIconElement=void 0;const r=i(249),o=i(26),n=i(23),l={color:1,fill:1,stroke:1},a={STROKE:{trackAttr:"data-follow-stroke",rawAttr:"stroke"},FILL:{trackAttr:"data-follow-fill",rawAttr:"fill"}};class h extends r.LitElement{constructor(){super(...arguments),this.name="",this.identifyer="",this.size="1em"}get _width(){return this.width||this.size}get _height(){return this.height||this.size}get _stroke(){return this.stroke||this.color}get _fill(){return this.fill||this.color}get SVGConfig(){return(window.__iconpark__||{})[this.identifyer]||(window.__iconpark__||{})[this.name]||{viewBox:"0 0 0 0",content:""}}connectedCallback(){super.connectedCallback(),setTimeout((()=>{this.monkeyPatch("STROKE",!0),this.monkeyPatch("FILL",!0)}))}monkeyPatch(t,e){switch(t){case"STROKE":this.updateDOMByHand(this.strokeAppliedNodes,"STROKE",this._stroke,!!e);break;case"FILL":this.updateDOMByHand(this.fillAppliedNodes,"FILL",this._fill,!!e)}}updateDOMByHand(t,e,i,s){!i&&s||t&&t.forEach((t=>{i&&i===t.getAttribute(a[e].rawAttr)||t.setAttribute(a[e].rawAttr,i||t.getAttribute(a[e].trackAttr))}))}attributeChangedCallback(t,e,i){super.attributeChangedCallback(t,e,i),"name"===t||"identifyer"===t?setTimeout((()=>{this.monkeyPatch("STROKE"),this.monkeyPatch("FILL")})):l[t]&&(this.monkeyPatch("STROKE"),this.monkeyPatch("FILL"))}render(){return r.svg`<svg width=${this._width} height=${this._height} preserveAspectRatio="xMidYMid meet" xmlns="http://www.w3.org/2000/svg" fill=${this.SVGConfig.fill} viewBox=${this.SVGConfig.viewBox}>${n.unsafeSVG(this.SVGConfig.content)}</svg>`}}h.styles=r.css`:host {display: inline-flex; align-items: center; justify-content: center;} :host([spin]) svg {animation: iconpark-spin 1s infinite linear;} :host([spin][rtl]) svg {animation: iconpark-spin-rtl 1s infinite linear;} :host([rtl]) svg {transform: scaleX(-1);} @keyframes iconpark-spin {0% { -webkit-transform: rotate(0); transform: rotate(0);} 100% {-webkit-transform: rotate(360deg); transform: rotate(360deg);}} @keyframes iconpark-spin-rtl {0% {-webkit-transform: scaleX(-1) rotate(0); transform: scaleX(-1) rotate(0);} 100% {-webkit-transform: scaleX(-1) rotate(360deg); transform: scaleX(-1) rotate(360deg);}}`,s([o.property({reflect:!0})],h.prototype,"name",void 0),s([o.property({reflect:!0,attribute:"icon-id"})],h.prototype,"identifyer",void 0),s([o.property({reflect:!0})],h.prototype,"color",void 0),s([o.property({reflect:!0})],h.prototype,"stroke",void 0),s([o.property({reflect:!0})],h.prototype,"fill",void 0),s([o.property({reflect:!0})],h.prototype,"size",void 0),s([o.property({reflect:!0})],h.prototype,"width",void 0),s([o.property({reflect:!0})],h.prototype,"height",void 0),s([o.queryAll(`[${a.STROKE.trackAttr}]`)],h.prototype,"strokeAppliedNodes",void 0),s([o.queryAll(`[${a.FILL.trackAttr}]`)],h.prototype,"fillAppliedNodes",void 0),e.IconparkIconElement=h,customElements.get("iconpark-icon")||customElements.define("iconpark-icon",h)}},e={};function i(s){var r=e[s];if(void 0!==r)return r.exports;var o=e[s]={exports:{}};return t[s].call(o.exports,o,o.exports,i),o.exports}i.d=(t,e)=>{for(var s in e)i.o(e,s)&&!i.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:e[s]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),i.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i(409)})();
