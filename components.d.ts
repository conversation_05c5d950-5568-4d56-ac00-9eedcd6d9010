/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ADropdown: typeof import('@arco-design/web-vue')['Dropdown']
    AMenu: typeof import('@arco-design/web-vue')['Menu']
    AMenuItem: typeof import('@arco-design/web-vue')['MenuItem']
    ASplit: typeof import('@arco-design/web-vue')['Split']
    ATooltip: typeof import('@arco-design/web-vue')['Tooltip']
    ATypographyParagraph: typeof import('@arco-design/web-vue')['TypographyParagraph']
    CopyUrlFormatSelect: typeof import('./src/components/business/CopyUrlFormatSelect.vue')['default']
    FileIconPreview: typeof import('./src/components/FilePreview/FileIconPreview.vue')['default']
    FilePreview: typeof import('./src/components/FilePreview/FilePreview.vue')['default']
    FileUpload: typeof import('./src/components/FileUpload/FileUpload.vue')['default']
    FileUploadImageItem: typeof import('./src/components/FileItem/FileUploadImageItem/FileUploadImageItem.vue')['default']
    FileUploadingItem: typeof import('./src/components/FileItem/FileUploadingItem/FileUploadingItem.vue')['default']
    FileUploadLineItem: typeof import('./src/components/FileItem/FileUploadLineItem/FileUploadLineItem.vue')['default']
    GuideUse: typeof import('./src/components/GuideUse/GuideUse.vue')['default']
    InputButton: typeof import('./src/components/plugin/Button/InputButton.vue')['default']
    InputFileFormat: typeof import('./src/components/plugin/InputFormat/InputFileFormat.vue')['default']
    InputFormat: typeof import('./src/components/plugin/InputFormat/InputFormat.vue')['default']
    LazyImg: typeof import('./src/components/FilePreview/preview/LazyImg.vue')['default']
    LazyLoader: typeof import('./src/components/common/container/LazyLoader/LazyLoader.vue')['default']
    LeftMenu: typeof import('./src/components/common/LeftMenu/LeftMenu.vue')['default']
    LeftMenuSplitPanel: typeof import('./src/components/common/container/LeftMenuSplitPanel/LeftMenuSplitPanel.vue')['default']
    RadioGroup: typeof import('./src/components/plugin/RadioGroup/RadioGroup.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SelectSearch: typeof import('./src/components/plugin/select/SelectSearch.vue')['default']
    SettingDivision: typeof import('./src/components/common/menu/SettingDivision/SettingDivision.vue')['default']
    SettingGroup: typeof import('./src/components/common/menu/SettingGroup/SettingGroup.vue')['default']
    SettingItem: typeof import('./src/components/common/menu/SettingItem/SettingItem.vue')['default']
    UContextMenu: typeof import('./src/components/common/contextMenu/UContextMenu.vue')['default']
    WheelWaveContainer: typeof import('./src/components/common/container/WheelWaveContainer/WheelWaveContainer.vue')['default']
  }
}
